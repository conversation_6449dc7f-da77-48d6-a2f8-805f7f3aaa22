---
inclusion: always
---

# ArchScope - Architecture Eagle Eye

ArchScope is a developer-focused architecture observation and governance system that provides a global perspective on projects through automated analysis of design documents and code repositories, helping developers quickly understand and govern their projects.

## Product Vision
Enable developers to maintain architectural clarity and quality through intelligent, automated analysis and documentation generation.

## Core Features

### 1. Intelligent Code Analysis
- LLM-powered repository parsing with specialized prompts
- Multi-language support (Java, TypeScript, Python, Go, etc.)
- Architecture pattern detection
- Dependency analysis and visualization

### 2. Automated Documentation Generation
- Architecture documentation (C4 models, system diagrams)
- API documentation extraction
- User manuals and extension guides
- llms.txt generation for AI consumption

### 3. Version Change Tracking
- Git-based change detection
- Document versioning and comparison
- Impact analysis of changes
- Historical evolution tracking

### 4. Health Assessment
- Project health scoring (star rating system)
- Code quality metrics
- Architecture compliance checks
- Technical debt identification

### 5. Documentation Website
- Automated static site generation
- Multi-format document rendering (Markdown, Mermaid, etc.)
- Search and navigation capabilities
- Version-aware documentation

## Domain Model

### Core Entities
- **Project**: Git repository being analyzed
- **Task**: Analysis job (code analysis, document generation)
- **Document**: Generated artifact from analysis
- **DocumentVersion**: Tracks document changes over time

### Key Value Objects
- **RepositoryUrl**: Validated Git repository URL
- **TaskStatus**: PENDING, PROCESSING, COMPLETED, FAILED
- **DocumentType**: API, ARCHITECTURE, USER_MANUAL, EXTENSION_GUIDE, LLMS_TXT

## User Journey

### Primary Flow
1. **Project Registration**: User submits Git repository URL
2. **Automated Analysis**: System clones repo and runs analysis tasks
3. **Document Generation**: LLM generates various document types
4. **Review & Access**: User views generated documentation
5. **Continuous Updates**: System monitors for changes and updates docs

### Secondary Flows
- Document comparison between versions
- Manual task triggering
- Health score monitoring
- Documentation export

## Business Rules

### Project Management
- Each project must have a unique, valid Git repository URL
- Projects can have multiple analysis tasks running concurrently
- Failed tasks can be retried with exponential backoff

### Document Generation
- Documents are versioned based on repository commits
- Each document type has specific generation prompts
- Generated content is cached to avoid redundant processing

### Quality Assurance
- All generated documents must pass validation
- Health scores are calculated based on multiple metrics
- System maintains audit logs for all operations

## Success Metrics
- Time to understand new codebase (target: < 30 minutes)
- Documentation freshness (target: < 24 hours lag)
- User adoption rate among development teams
- Reduction in architecture-related questions/issues