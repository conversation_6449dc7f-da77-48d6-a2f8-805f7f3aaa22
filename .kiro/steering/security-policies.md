---
inclusion: always
---

# Security Policies

## Authentication & Authorization

### Current State (MVP)
- No authentication required for public demo
- All endpoints are publicly accessible
- Focus on functionality over security

### Future Implementation
- JWT-based authentication
- Role-based access control (RBAC)
- API key authentication for programmatic access

## Input Validation

### Git Repository URLs
- Must match valid Git URL patterns
- Support HTTP/HTTPS and SSH formats
- Validate against known Git hosting providers
- Prevent access to internal/private networks

### API Input Validation
```java
// Example validation annotations
@Valid
@NotNull
@Pattern(regexp = "^https?://.*\\.git$")
private String repositoryUrl;

@Size(min = 1, max = 255)
@NotBlank
private String projectName;
```

### SQL Injection Prevention
- Use parameterized queries with MyBatis
- Never concatenate user input into SQL strings
- Validate all database inputs
- Use prepared statements exclusively

## Data Security

### Sensitive Data Handling
- No passwords stored (no authentication yet)
- Git credentials handled securely when implemented
- Temporary files cleaned up after processing
- Logs sanitized of sensitive information

### Database Security
- Use connection pooling with Druid
- Database credentials in environment variables
- Regular backup and recovery procedures
- Encrypt sensitive data at rest (future)

## Code Security Practices

### Java Backend
```java
// Secure random generation
SecureRandom random = new SecureRandom();

// Input sanitization
String sanitized = StringEscapeUtils.escapeHtml4(userInput);

// Resource cleanup
try (InputStream is = new FileInputStream(file)) {
    // Process file
} // Automatically closed
```

### Frontend Security
- Sanitize all user inputs before display
- Use Content Security Policy (CSP) headers
- Validate data on both client and server
- Prevent XSS through proper escaping

## Infrastructure Security

### Docker Security
- Use non-root users in containers
- Scan images for vulnerabilities
- Keep base images updated
- Limit container capabilities

### Network Security
- Use HTTPS in production
- Implement proper CORS policies
- Rate limiting on API endpoints
- Monitor for suspicious activity

## Vulnerability Management

### Dependency Scanning
- Regular dependency updates
- Use tools like OWASP Dependency Check
- Monitor security advisories
- Automated vulnerability scanning in CI

### Code Analysis
- Static code analysis with SonarQube
- Security-focused code reviews
- Regular penetration testing (production)
- Security training for developers

## Incident Response

### Security Incident Handling
1. **Detection**: Monitor logs and alerts
2. **Assessment**: Evaluate impact and scope
3. **Containment**: Isolate affected systems
4. **Recovery**: Restore normal operations
5. **Lessons Learned**: Update security measures

### Logging & Monitoring
- Log all authentication attempts
- Monitor API usage patterns
- Alert on suspicious activities
- Maintain audit trails

## Compliance Considerations

### Data Privacy
- Minimal data collection
- Clear data retention policies
- User consent for data processing
- Right to data deletion

### Security Standards
- Follow OWASP Top 10 guidelines
- Implement security by design
- Regular security assessments
- Document security procedures