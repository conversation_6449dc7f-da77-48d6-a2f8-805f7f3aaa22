---
inclusion: always
---

# Code Review Standards

## Pre-Review Checklist
- [ ] All tests pass (`mvn test` for backend, `npm run test` for frontend)
- [ ] Code coverage ≥ 80% for new code
- [ ] No ESLint/SonarLint errors or warnings
- [ ] Documentation updated (README, API docs, inline comments)
- [ ] Database migrations included if schema changes
- [ ] No hardcoded values or secrets

## Review Criteria

### Architecture & Design
- [ ] Follows DDD and hexagonal architecture principles
- [ ] Proper layer separation (domain, application, infrastructure)
- [ ] Single responsibility principle applied
- [ ] Dependencies point inward (domain is independent)

### Code Quality
- [ ] Meaningful variable and method names
- [ ] Functions are small and focused (< 20 lines)
- [ ] No code duplication
- [ ] Proper error handling and logging
- [ ] Thread-safe code where applicable

### Security
- [ ] Input validation implemented
- [ ] No SQL injection vulnerabilities
- [ ] Sensitive data not logged
- [ ] Proper access control checks

### Performance
- [ ] Database queries optimized
- [ ] No N+1 query problems
- [ ] Appropriate caching strategies
- [ ] Resource cleanup (connections, streams)

### Testing
- [ ] Unit tests for business logic
- [ ] Integration tests for repositories
- [ ] E2E tests for critical user flows
- [ ] Test data cleanup

## Review Process
1. **Self-review**: Author reviews own code before requesting review
2. **Automated checks**: CI pipeline must pass
3. **Peer review**: At least one team member approval required
4. **Architecture review**: For significant changes, architect approval needed

## Common Issues to Flag
- Mixing concerns across layers
- Missing error handling
- Inadequate test coverage
- Performance bottlenecks
- Security vulnerabilities
- Inconsistent naming conventions