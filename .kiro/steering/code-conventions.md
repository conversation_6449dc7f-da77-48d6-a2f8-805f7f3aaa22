---
inclusion: always
---

# Code Conventions

## 编码规范
- Follow SOLID, DRY & SRP.
- KISS (Keep It Simple, Stupid)
    - Encourages to write straightforward,uncomplicated solutions
    - Avoids over-engineering and unnecessary complexity
    - Results in more readable and maintainable code
- YAGNI (You Aren't Gonna Need It)
    - Prevents from adding speculative features
    - Focuses on implementing only what's currently needed
    - Reduces code bloat and maintenance overhead
- SOLID Principles
    - Single Responsibility Principle
    - Open-Closed Principle
    - Liskov Substitution Principle
    - Interface Segregation Principle
    - Dependency Inversion Principle

## Java Backend Conventions

### Naming Conventions
- **Classes**: PascalCase (`ProjectService`, `TaskRepository`)
- **Methods/Variables**: camelCase (`getProjectById`, `repositoryUrl`)
- **Constants**: UPPER_SNAKE_CASE (`MAX_RETRY_COUNT`)
- **Packages**: lowercase with dots (`com.archscope.domain.model`)

### File Organization
```
src/main/java/com/archscope/
├── domain/
│   ├── model/           # Domain entities
│   ├── valueobject/     # Value objects
│   ├── service/         # Domain services
│   └── repository/      # Repository interfaces
├── app/
│   ├── service/         # Application services
│   ├── controller/      # REST controllers
│   └── dto/            # Data transfer objects
└── infrastructure/
    ├── persistence/     # JPA entities
    └── repository/      # Repository implementations
```

### Import Order
1. Java standard library
2. Third-party libraries
3. Spring framework
4. Project packages (com.archscope.*)

### Code Style
- Use `@Override` annotations
- Prefer composition over inheritance
- Keep methods under 20 lines
- Use meaningful variable names
- Add JavaDoc for public APIs

## TypeScript/Vue Frontend Conventions

### Naming Conventions
- **Components**: PascalCase (`ProjectDetail.vue`)
- **Files**: camelCase (`projectUtils.ts`)
- **Variables/Functions**: camelCase (`getProjectList`)
- **CSS Classes**: kebab-case (`project-card`)
- **Constants**: UPPER_SNAKE_CASE (`API_BASE_URL`)

### File Organization
```
src/
├── components/         # Reusable Vue components
├── views/             # Page components
├── composables/       # Vue composition functions
├── stores/            # Pinia stores
├── utils/             # Utility functions
├── types/             # TypeScript type definitions
└── api/               # API client functions
```

### Vue Component Structure
```vue
<template>
  <!-- Template content -->
</template>

<script setup lang="ts">
// Imports
// Props/Emits
// Reactive data
// Computed properties
// Methods
// Lifecycle hooks
</script>

<style scoped>
/* Component styles */
</style>
```

### TypeScript Guidelines
- Use strict mode
- Define interfaces for all data structures
- Prefer `type` over `interface` for unions
- Use generic types where appropriate
- Avoid `any` type

## Common Patterns

### Error Handling
- Use custom exception classes in Java
- Implement global error handlers
- Log errors with context information
- Return consistent error responses

### Async Operations
- Use `CompletableFuture` in Java
- Use `async/await` in TypeScript
- Handle loading states in UI
- Implement proper error boundaries

### Anti-Patterns to Avoid
- God classes/components
- Deep nesting (max 3 levels)
- Magic numbers/strings
- Tight coupling between layers
- Mixing business logic with presentation