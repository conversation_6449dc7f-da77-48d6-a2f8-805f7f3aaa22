---
inclusion: always
---

# 技术栈

## 后端

- **语言**：Java 8
- **框架**：Spring Boot 2.7.x
- **架构**：领域驱动设计（DDD）与六边形架构
- **构建工具**：Maven
- **数据库**：MySQL 8.0+
- **缓存**：Redis
- **消息队列**：RocketMQ
- **ORM**：MyBatis Plus 3.5.x
- **连接池**：Druid
- **数据库迁移**：Flyway
- **对象映射**：MapStruct
- **测试**：JUnit, ArchUnit

## 前端

- **语言**：TypeScript
- **框架**：Vue 3.x 与 Composition API
- **构建工具**：Vite
- **状态管理**：Pinia
- **UI框架**：Element Plus, Tailwind CSS
- **HTTP客户端**：Axios
- **测试**：Cypress 端到端测试
- **可视化**：Mermaid 图表
- **代码高亮**：highlight.js

## MCP集成

- **MCP服务器**：基于Node.js的MCP服务器用于任务处理

## 常用命令

### 后端

```bash
# 构建整个项目
mvn clean install -DskipTests

# 运行应用
cd arch-scope-main
mvn spring-boot:run

# 运行测试
mvn test

# 数据库迁移
mvn flyway:migrate
```

### 前端

```bash
# 安装依赖
cd arch-scope-frontend
npm install

# 开发服务器
npm run dev

# 生产构建
npm run build

# 运行E2E测试
npm run test:e2e

# 使用UI运行E2E测试
npm run test:e2e:open

# 使用模拟服务器运行
npm run dev:full
```

### Docker

```bash
# 启动必需的服务
docker-compose up -d mysql redis

# 启动所有服务（包括RocketMQ）
docker-compose up -d
```

## 环境要求

- Java 8
- Node.js 16+
- MySQL 8.0+
- Redis 6+
- RocketMQ 4+（可选，系统有容错机制）