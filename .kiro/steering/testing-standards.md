---
inclusion: always
---

# Testing Standards

## Testing Strategy

### Test Pyramid
1. **Unit Tests (70%)**: Fast, isolated, focused on single components
2. **Integration Tests (20%)**: Test component interactions
3. **E2E Tests (10%)**: Full user journey validation

### Coverage Requirements
- **Minimum**: 80% line coverage for new code
- **Target**: 90% line coverage for critical business logic
- **Exclusions**: Configuration classes, DTOs, generated code

## Backend Testing (Java)

### Unit Testing Framework
```xml
<dependency>
    <groupId>org.junit.jupiter</groupId>
    <artifactId>junit-jupiter</artifactId>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-core</artifactId>
    <scope>test</scope>
</dependency>
```

### Test Structure
```java
@ExtendWith(MockitoExtension.class)
class ProjectServiceTest {
    
    @Mock
    private ProjectRepository projectRepository;
    
    @InjectMocks
    private ProjectService projectService;
    
    @Test
    @DisplayName("Should create project when valid repository URL provided")
    void shouldCreateProject_WhenValidRepositoryUrl() {
        // Given
        String repositoryUrl = "https://github.com/user/repo.git";
        Project expectedProject = new Project(repositoryUrl);
        when(projectRepository.save(any(Project.class))).thenReturn(expectedProject);
        
        // When
        Project result = projectService.createProject(repositoryUrl);
        
        // Then
        assertThat(result.getRepositoryUrl()).isEqualTo(repositoryUrl);
        verify(projectRepository).save(any(Project.class));
    }
}
```

### Integration Testing
```java
@SpringBootTest
@Testcontainers
class ProjectRepositoryIntegrationTest {
    
    @Container
    static MySQLContainer<?> mysql = new MySQLContainer<>("mysql:8.0")
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");
    
    @Autowired
    private ProjectRepository projectRepository;
    
    @Test
    void shouldPersistProject() {
        // Test database operations
    }
}
```

### Mocking Guidelines
- Mock external dependencies (databases, HTTP clients)
- Don't mock value objects or entities
- Use `@MockBean` for Spring context tests
- Verify interactions with mocks
- Reset mocks between tests

## Frontend Testing (Vue/TypeScript)

### Testing Framework
```json
{
  "devDependencies": {
    "@vue/test-utils": "^2.0.0",
    "vitest": "^0.34.0",
    "jsdom": "^22.0.0",
    "cypress": "^13.0.0"
  }
}
```

### Component Testing
```typescript
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import ProjectCard from '@/components/ProjectCard.vue'

describe('ProjectCard', () => {
  it('displays project information correctly', () => {
    const project = {
      id: 1,
      name: 'Test Project',
      repositoryUrl: 'https://github.com/test/repo.git'
    }
    
    const wrapper = mount(ProjectCard, {
      props: { project }
    })
    
    expect(wrapper.text()).toContain('Test Project')
    expect(wrapper.find('[data-testid="repo-url"]').text())
      .toBe('https://github.com/test/repo.git')
  })
})
```

### E2E Testing (Cypress)
```typescript
describe('Project Registration Flow', () => {
  it('should register new project successfully', () => {
    cy.visit('/register-project')
    cy.get('[data-testid="repository-url-input"]')
      .type('https://github.com/test/repo.git')
    cy.get('[data-testid="submit-button"]').click()
    
    cy.url().should('include', '/projects/')
    cy.contains('Project registered successfully').should('be.visible')
  })
})
```

## Test Organization

### File Structure
```
src/test/java/
├── unit/                    # Unit tests
│   ├── domain/             # Domain layer tests
│   ├── app/                # Application layer tests
│   └── infrastructure/     # Infrastructure tests
├── integration/            # Integration tests
└── e2e/                   # End-to-end tests

cypress/
├── e2e/                   # E2E test specs
├── fixtures/              # Test data
└── support/               # Helper functions
```

### Naming Conventions
- Test classes: `{ClassUnderTest}Test`
- Test methods: `should{ExpectedBehavior}_When{Condition}`
- Test files: `{component}.test.ts` or `{component}.spec.ts`

## Test Data Management

### Test Fixtures
```java
public class ProjectTestData {
    public static Project validProject() {
        return Project.builder()
            .repositoryUrl("https://github.com/test/repo.git")
            .name("Test Project")
            .build();
    }
}
```

### Database Testing
- Use Testcontainers for integration tests
- Clean database state between tests
- Use transactions that rollback
- Separate test database from development

## Continuous Integration

### Test Execution
```bash
# Backend tests
mvn test                    # Unit tests
mvn verify                  # Integration tests
mvn test -Dtest=**/*E2ETest # E2E tests

# Frontend tests
npm run test:unit          # Unit tests
npm run test:e2e          # E2E tests
npm run test:coverage     # Coverage report
```

### Quality Gates
- All tests must pass before merge
- Coverage threshold must be met
- No flaky tests allowed
- Performance tests for critical paths

## Best Practices

### General Guidelines
- Write tests before or alongside code (TDD/BDD)
- Keep tests simple and focused
- Use descriptive test names
- Test behavior, not implementation
- Maintain test independence

### Common Anti-Patterns to Avoid
- Testing implementation details
- Overly complex test setup
- Shared mutable state between tests
- Testing multiple concerns in one test
- Ignoring or skipping failing tests