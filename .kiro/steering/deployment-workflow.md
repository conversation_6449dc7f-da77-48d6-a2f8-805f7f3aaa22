---
inclusion: always
---

# Deployment Workflow

## Environment Strategy
- **Development**: Local development with Docker Compose
- **Staging**: Pre-production testing environment
- **Production**: Live system (future)

## Build Process

### Backend (Java/Maven)
```bash
# Clean and build
mvn clean install -DskipTests

# Run tests
mvn test

# Package application
mvn package -DskipTests

# Build Docker image
docker build -t archscope-backend:latest .
```

### Frontend (Vue/Vite)
```bash
# Install dependencies
npm ci

# Run tests
npm run test

# Build for production
npm run build

# Build Docker image
docker build -t archscope-frontend:latest .
```

## Docker Compose Setup

### Development Environment
```bash
# Start required services
docker-compose up -d mysql redis

# Start application services
docker-compose up -d backend frontend

# View logs
docker-compose logs -f
```

### Service Dependencies
1. **MySQL**: Database for persistent storage
2. **Redis**: Caching and session storage
3. **RocketMQ**: Message queue (optional, has fallback)
4. **Backend**: Spring Boot application
5. **Frontend**: Vue.js application

## Environment Configuration

### Required Environment Variables
```bash
# Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=archscope
DB_USERNAME=root
DB_PASSWORD=password

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# Application
SERVER_PORT=8080
FRONTEND_PORT=3000
API_BASE_URL=http://localhost:8080
```

## Health Checks
- **Backend**: `GET /actuator/health`
- **Frontend**: `GET /health`
- **Database**: Connection test on startup
- **Redis**: Ping command

## Rollback Strategy
1. **Database**: Use Flyway migrations with rollback scripts
2. **Application**: Keep previous Docker images tagged
3. **Configuration**: Version control environment configs
4. **Quick rollback**: `docker-compose down && docker-compose up -d`

## Monitoring
- Application logs via Docker Compose
- Health endpoint monitoring
- Database connection monitoring
- Resource usage tracking

## Deployment Checklist
- [ ] All tests pass
- [ ] Database migrations applied
- [ ] Environment variables configured
- [ ] Health checks passing
- [ ] Backup created (production)
- [ ] Rollback plan prepared