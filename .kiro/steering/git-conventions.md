---
inclusion: always
---

# Git Conventions

## Commit Message Format
```
<type>(<scope>): <subject>

<body>

<footer>
```

### Types
- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code formatting (no logic changes)
- **refactor**: Code refactoring
- **test**: Adding or updating tests
- **chore**: Build process, dependencies, tooling

### Scopes (Optional)
- **domain**: Domain layer changes
- **app**: Application layer changes
- **infra**: Infrastructure layer changes
- **frontend**: Frontend changes
- **api**: API changes

### Examples
```
feat(domain): add project analysis task entity

fix(api): resolve project registration validation error

docs: update API documentation for task endpoints

test(domain): add unit tests for project service
```

## Branch Strategy

### Main Branches
- **main**: Production-ready code
- **develop**: Integration branch for features

### Supporting Branches
- **feature/\<feature-name\>**: New feature development
  - Branch from: `develop`
  - Merge to: `develop`
  - Example: `feature/project-registration`

- **hotfix/\<issue-name\>**: Critical production fixes
  - Branch from: `main`
  - Merge to: `main` and `develop`
  - Example: `hotfix/security-vulnerability`

- **release/\<version\>**: Release preparation
  - Branch from: `develop`
  - Merge to: `main` and `develop`
  - Example: `release/v1.0.0`

## Workflow

### Feature Development
1. Create feature branch from `develop`
2. Implement feature with tests
3. Create pull request to `develop`
4. Code review and approval
5. Merge to `develop`

### Release Process
1. Create release branch from `develop`
2. Final testing and bug fixes
3. Update version numbers
4. Merge to `main` and tag
5. Merge back to `develop`

## Pull Request Guidelines
- Use descriptive titles
- Include issue references (#123)
- Add screenshots for UI changes
- Ensure CI checks pass
- Request appropriate reviewers
- Keep PRs focused and small

## Commit Best Practices
- Make atomic commits (one logical change)
- Write clear, descriptive messages
- Use present tense ("add feature" not "added feature")
- Reference issues when applicable
- Keep commits focused and small