package com.archscope.infrastructure.cache;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * Redis服务 - 简化版本，不使用Redis，所有方法都是空实现
 */
@Service
public class RedisService {

    private static final Logger log = LoggerFactory.getLogger(RedisService.class);

    /**
     * 存储缓存对象（空实现）
     */
    public void set(String key, Object value) {
        log.debug("Redis缓存已禁用: key={}", key);
    }

    /**
     * 存储缓存对象并设置过期时间（空实现）
     */
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        log.debug("Redis缓存已禁用: key={}, timeout={} {}", key, timeout, unit);
    }

    /**
     * 获取缓存对象（总是返回null）
     */
    public Object get(String key) {
        log.debug("Redis缓存已禁用，返回null: key={}", key);
        return null;
    }

    /**
     * 获取指定类型的缓存对象（总是返回null）
     */
    public <T> T get(String key, Class<T> clazz) {
        log.debug("Redis缓存已禁用，返回null: key={}, class={}", key, clazz.getName());
        return null;
    }

    /**
     * 删除缓存（总是返回false）
     */
    public Boolean delete(String key) {
        log.debug("Redis缓存已禁用，无需删除: key={}", key);
        return false;
    }

    /**
     * 批量删除缓存（总是返回0）
     */
    public Long delete(List<String> keys) {
        log.debug("Redis缓存已禁用，无需删除: keys={}", keys);
        return 0L;
    }

    /**
     * 设置过期时间（总是返回false）
     */
    public Boolean expire(String key, long timeout, TimeUnit unit) {
        log.debug("Redis缓存已禁用，无需设置过期时间: key={}", key);
        return false;
    }

    /**
     * 获取过期时间（总是返回-1）
     */
    public Long getExpire(String key, TimeUnit unit) {
        log.debug("Redis缓存已禁用，返回-1: key={}", key);
        return -1L;
    }

    /**
     * 判断key是否存在（总是返回false）
     */
    public Boolean hasKey(String key) {
        log.debug("Redis缓存已禁用，返回false: key={}", key);
        return false;
    }

    /**
     * 递增（总是返回1）
     */
    public Long increment(String key, long delta) {
        log.debug("Redis缓存已禁用，返回1: key={}, delta={}", key, delta);
        return 1L;
    }

    /**
     * 递减（总是返回1）
     */
    public Long decrement(String key, long delta) {
        log.debug("Redis缓存已禁用，返回1: key={}, delta={}", key, delta);
        return 1L;
    }

    /**
     * 获取匹配的所有key（总是返回空集合）
     */
    public Set<String> keys(String pattern) {
        log.debug("Redis缓存已禁用，返回空集合: pattern={}", pattern);
        return Collections.emptySet();
    }

    /**
     * 存储List（空实现）
     */
    public <T> void setList(String key, List<T> list) {
        log.debug("Redis缓存已禁用: key={}, list size={}", key, list != null ? list.size() : 0);
    }

    /**
     * 存储List并设置过期时间（空实现）
     */
    public <T> void setList(String key, List<T> list, long timeout, TimeUnit unit) {
        log.debug("Redis缓存已禁用: key={}, list size={}, timeout={} {}", 
                key, list != null ? list.size() : 0, timeout, unit);
    }

    /**
     * 获取List（总是返回null）
     */
    public <T> List<T> getList(String key, Class<T> clazz) {
        log.debug("Redis缓存已禁用，返回null: key={}, class={}", key, clazz.getSimpleName());
        return null;
    }

    /**
     * 按模式删除（空实现）
     */
    public void deleteByPattern(String pattern) {
        log.debug("Redis缓存已禁用，无需按模式删除: pattern={}", pattern);
    }
}
