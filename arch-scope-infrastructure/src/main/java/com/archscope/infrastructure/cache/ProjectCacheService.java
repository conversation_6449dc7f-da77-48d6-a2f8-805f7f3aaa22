package com.archscope.infrastructure.cache;

import com.archscope.domain.entity.Project;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;

/**
 * 项目缓存服务 - 简化版本，不使用Redis
 */
@Service
public class ProjectCacheService {

    private static final Logger log = LoggerFactory.getLogger(ProjectCacheService.class);

    /**
     * 缓存项目（空实现）
     */
    public void cacheProject(Project project) {
        // 不使用缓存，直接返回
        log.debug("项目缓存已禁用: projectId={}", project != null ? project.getId() : null);
    }

    /**
     * 根据ID获取缓存的项目（总是返回空）
     */
    public Optional<Project> getProjectById(Long id) {
        // 不使用缓存，总是返回空
        log.debug("项目缓存已禁用，返回空: projectId={}", id);
        return Optional.empty();
    }

    /**
     * 根据仓库URL获取缓存的项目（总是返回空）
     */
    public Optional<Project> getProjectByRepositoryUrl(String repositoryUrl) {
        // 不使用缓存，总是返回空
        log.debug("项目缓存已禁用，返回空: repositoryUrl={}", repositoryUrl);
        return Optional.empty();
    }

    /**
     * 缓存项目列表（空实现）
     */
    public void cacheProjectList(List<Project> projects) {
        // 不使用缓存，直接返回
        log.debug("项目列表缓存已禁用: size={}", projects != null ? projects.size() : 0);
    }

    /**
     * 获取缓存的项目列表（总是返回空）
     */
    public Optional<List<Project>> getAllProjects() {
        // 不使用缓存，总是返回空
        log.debug("项目列表缓存已禁用，返回空");
        return Optional.empty();
    }

    /**
     * 移除项目缓存（空实现）
     */
    public void removeProjectCache(Long id) {
        // 不使用缓存，直接返回
        log.debug("项目缓存已禁用，无需移除: projectId={}", id);
    }

    /**
     * 移除所有项目缓存（空实现）
     */
    public void clearAllProjectCache() {
        // 不使用缓存，直接返回
        log.debug("项目缓存已禁用，无需清除");
    }
}