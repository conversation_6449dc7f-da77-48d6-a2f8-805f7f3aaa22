package com.archscope.infrastructure.cache;

import com.archscope.domain.model.servicediscovery.Capability;
import com.archscope.domain.model.servicediscovery.Service;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 服务发现专用缓存服务 - 简化版本，不使用Redis
 */
@Component
public class ServiceDiscoveryCacheService {

    private static final Logger log = LoggerFactory.getLogger(ServiceDiscoveryCacheService.class);

    /**
     * 缓存单个服务（空实现）
     */
    public void cacheService(String serviceId, Service service) {
        log.debug("服务缓存已禁用: serviceId={}", serviceId);
    }

    /**
     * 获取缓存的服务（总是返回null）
     */
    public Service getCachedService(String serviceId) {
        log.debug("服务缓存已禁用，返回null: serviceId={}", serviceId);
        return null;
    }

    /**
     * 缓存服务列表（空实现）
     */
    public void cacheServiceList(String cacheKey, List<Service> services) {
        log.debug("服务列表缓存已禁用: cacheKey={}, size={}", cacheKey, services != null ? services.size() : 0);
    }

    /**
     * 获取缓存的服务列表（总是返回null）
     */
    public List<Service> getCachedServiceList(String cacheKey) {
        log.debug("服务列表缓存已禁用，返回null: cacheKey={}", cacheKey);
        return null;
    }

    /**
     * 缓存能力（空实现）
     */
    public void cacheCapability(String capabilityId, Capability capability) {
        log.debug("能力缓存已禁用: capabilityId={}", capabilityId);
    }

    /**
     * 获取缓存的能力（总是返回null）
     */
    public Capability getCachedCapability(String capabilityId) {
        log.debug("能力缓存已禁用，返回null: capabilityId={}", capabilityId);
        return null;
    }

    /**
     * 缓存能力列表（空实现）
     */
    public void cacheCapabilityList(String cacheKey, List<Capability> capabilities) {
        log.debug("能力列表缓存已禁用: cacheKey={}, size={}", cacheKey, capabilities != null ? capabilities.size() : 0);
    }

    /**
     * 获取缓存的能力列表（总是返回null）
     */
    public List<Capability> getCachedCapabilityList(String cacheKey) {
        log.debug("能力列表缓存已禁用，返回null: cacheKey={}", cacheKey);
        return null;
    }

    /**
     * 清除服务相关缓存（空实现）
     */
    public void clearServiceCache(String serviceId) {
        log.debug("服务缓存已禁用，无需清除: serviceId={}", serviceId);
    }

    /**
     * 清除能力相关缓存（空实现）
     */
    public void clearCapabilityCache(String capabilityId) {
        log.debug("能力缓存已禁用，无需清除: capabilityId={}", capabilityId);
    }

    /**
     * 清除所有缓存（空实现）
     */
    public void clearAllCache() {
        log.debug("缓存已禁用，无需清除");
    }
}
