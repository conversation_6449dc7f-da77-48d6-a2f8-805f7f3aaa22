package com.archscope.infrastructure.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 服务发现性能优化配置
 */
@Configuration
@EnableAsync
public class ServiceDiscoveryPerformanceConfig {

    /**
     * MyBatis Plus性能优化插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 分页插件
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor();
        paginationInterceptor.setMaxLimit(1000L); // 最大分页限制
        paginationInterceptor.setOverflow(false); // 溢出总页数后是否进行处理
        interceptor.addInnerInterceptor(paginationInterceptor);
        
        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        
        // 防止全表更新与删除插件
        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());
        
        return interceptor;
    }

    /**
     * 异步任务执行器 - 用于批量操作
     */
    @Bean("serviceDiscoveryTaskExecutor")
    public Executor serviceDiscoveryTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(5);
        
        // 最大线程数
        executor.setMaxPoolSize(20);
        
        // 队列容量
        executor.setQueueCapacity(100);
        
        // 线程名前缀
        executor.setThreadNamePrefix("ServiceDiscovery-");
        
        // 拒绝策略：由调用线程处理
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        return executor;
    }

    /**
     * 缓存任务执行器 - 用于异步缓存操作
     */
    @Bean("cacheTaskExecutor")
    public Executor cacheTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(2);
        
        // 最大线程数
        executor.setMaxPoolSize(10);
        
        // 队列容量
        executor.setQueueCapacity(50);
        
        // 线程名前缀
        executor.setThreadNamePrefix("Cache-");
        
        // 拒绝策略：丢弃最老的任务
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.DiscardOldestPolicy());
        
        executor.initialize();
        return executor;
    }

    /**
     * 数据库连接池优化配置
     */
    @Bean
    public DruidDataSourceConfig druidDataSourceConfig() {
        return new DruidDataSourceConfig();
    }

    /**
     * Druid数据源配置类
     */
    public static class DruidDataSourceConfig {
        
        /**
         * 获取优化的Druid配置属性
         */
        public java.util.Properties getDruidProperties() {
            java.util.Properties properties = new java.util.Properties();
            
            // 初始化连接数
            properties.setProperty("druid.initialSize", "10");
            
            // 最小连接数
            properties.setProperty("druid.minIdle", "10");
            
            // 最大连接数
            properties.setProperty("druid.maxActive", "50");
            
            // 获取连接等待超时时间
            properties.setProperty("druid.maxWait", "60000");
            
            // 间隔多久进行一次检测，检测需要关闭的空闲连接
            properties.setProperty("druid.timeBetweenEvictionRunsMillis", "60000");
            
            // 连接保持空闲而不被驱逐的最小时间
            properties.setProperty("druid.minEvictableIdleTimeMillis", "300000");
            
            // 验证连接有效性的SQL
            properties.setProperty("druid.validationQuery", "SELECT 1");
            
            // 申请连接时执行validationQuery检测连接是否有效
            properties.setProperty("druid.testWhileIdle", "true");
            
            // 申请连接时执行validationQuery检测连接是否有效
            properties.setProperty("druid.testOnBorrow", "false");
            
            // 归还连接时执行validationQuery检测连接是否有效
            properties.setProperty("druid.testOnReturn", "false");
            
            // 打开PSCache，并且指定每个连接上PSCache的大小
            properties.setProperty("druid.poolPreparedStatements", "true");
            properties.setProperty("druid.maxPoolPreparedStatementPerConnectionSize", "20");
            
            // 配置监控统计拦截的filters
            properties.setProperty("druid.filters", "stat,wall,slf4j");
            
            // 慢SQL记录
            properties.setProperty("druid.connectionProperties", 
                    "druid.stat.mergeSql=true;druid.stat.slowSqlMillis=1000");
            
            return properties;
        }
    }

    // Redis连接池配置已禁用
}