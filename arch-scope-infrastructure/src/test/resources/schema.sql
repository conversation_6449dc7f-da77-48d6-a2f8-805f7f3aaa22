-- 测试环境数据库表结构
-- 项目表
CREATE TABLE IF NOT EXISTS project (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    repository_url VARCHAR(500) NOT NULL,
    normalized_repository_url VARCHAR(500),
    branch VARCHAR(255) DEFAULT 'main',
    creator_id BIGINT,
    status VARCHAR(50) DEFAULT 'PENDING_ANALYSIS',
    active BOOLEAN DEFAULT TRUE,
    type VARCHAR(50) DEFAULT 'OTHER',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为项目表创建索引
CREATE UNIQUE INDEX IF NOT EXISTS uk_normalized_repository_url ON project(normalized_repository_url);
CREATE INDEX IF NOT EXISTS idx_repository_url ON project(repository_url);
CREATE INDEX IF NOT EXISTS idx_creator_id ON project(creator_id);
CREATE INDEX IF NOT EXISTS idx_status ON project(status);
CREATE INDEX IF NOT EXISTS idx_active ON project(active);
CREATE INDEX IF NOT EXISTS idx_created_at ON project(created_at);

-- 文档版本表
CREATE TABLE IF NOT EXISTS document_version (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    project_id BIGINT NOT NULL,
    doc_type VARCHAR(50) NOT NULL,
    commit_id VARCHAR(255) NOT NULL,
    content_path VARCHAR(500),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version_tag VARCHAR(255),
    title VARCHAR(255),
    description TEXT,
    is_published BOOLEAN DEFAULT FALSE,

    -- 外键约束
    FOREIGN KEY (project_id) REFERENCES project(id) ON DELETE CASCADE
);

-- 为文档版本表创建索引
CREATE INDEX IF NOT EXISTS idx_project_id ON document_version(project_id);
CREATE INDEX IF NOT EXISTS idx_doc_type ON document_version(doc_type);
CREATE INDEX IF NOT EXISTS idx_commit_id ON document_version(commit_id);
CREATE INDEX IF NOT EXISTS idx_timestamp ON document_version(timestamp);
CREATE INDEX IF NOT EXISTS idx_is_published ON document_version(is_published);
CREATE UNIQUE INDEX IF NOT EXISTS uk_project_doc_commit ON document_version(project_id, doc_type, commit_id);

-- 任务表
CREATE TABLE IF NOT EXISTS tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255),
    description TEXT,
    task_type VARCHAR(50) NOT NULL,
    status VARCHAR(30) NOT NULL,
    priority INT,
    progress INT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME,
    error_message TEXT,
    result_summary TEXT,
    parameters JSON,
    project_id BIGINT,
    created_by VARCHAR(100),
    assigned_to VARCHAR(100),

    -- LLM任务管理新增字段
    worker_id VARCHAR(100),
    worker_type VARCHAR(50),
    processing_started_at DATETIME,
    timeout_at DATETIME,
    overall_status VARCHAR(30),
    commit_id VARCHAR(40),
    results JSON,
    execution_time_ms BIGINT,
    retry_count INT DEFAULT 0,
    max_retries INT DEFAULT 3,
    last_error_detail TEXT,
    task_version VARCHAR(10) DEFAULT '1.0',

    -- 外键约束
    FOREIGN KEY (project_id) REFERENCES project(id) ON DELETE CASCADE
);

-- 为任务表创建索引
CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at);
CREATE INDEX IF NOT EXISTS idx_tasks_status_priority ON tasks(status, priority);
CREATE INDEX IF NOT EXISTS idx_tasks_worker_id ON tasks(worker_id);
CREATE INDEX IF NOT EXISTS idx_tasks_timeout_at ON tasks(timeout_at);
CREATE INDEX IF NOT EXISTS idx_tasks_processing_started_at ON tasks(processing_started_at);
CREATE INDEX IF NOT EXISTS idx_tasks_overall_status ON tasks(overall_status);

-- 代码仓库表
CREATE TABLE IF NOT EXISTS code_repository (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    project_id BIGINT NOT NULL,
    repository_url VARCHAR(500) NOT NULL,
    branch VARCHAR(255) DEFAULT 'main',
    last_commit_id VARCHAR(255),
    last_sync_time TIMESTAMP NULL,
    status VARCHAR(50) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 外键约束
    FOREIGN KEY (project_id) REFERENCES project(id) ON DELETE CASCADE
);

-- 为代码仓库表创建索引
CREATE INDEX IF NOT EXISTS idx_repo_project_id ON code_repository(project_id);
CREATE INDEX IF NOT EXISTS idx_repo_url ON code_repository(repository_url);
CREATE INDEX IF NOT EXISTS idx_repo_branch ON code_repository(branch);
CREATE INDEX IF NOT EXISTS idx_repo_status ON code_repository(status);
CREATE INDEX IF NOT EXISTS idx_repo_last_sync_time ON code_repository(last_sync_time);
