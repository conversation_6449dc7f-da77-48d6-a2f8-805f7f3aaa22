#!/usr/bin/env node

/**
 * ArchScope MCP Service Entry Point
 * Main entry point for the ArchScope Model Context Protocol service
 */

// Load environment variables from .env file
import * as dotenv from 'dotenv';
dotenv.config();

import { ArchScopeMcpServer } from './server';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { ConfigurationError } from './utils/errors';

/**
 * Main function to start the ArchScope MCP service
 */
async function main(): Promise<void> {
  let server: ArchScopeMcpServer | null = null;
  
  try {
    console.log('🚀 Starting ArchScope MCP Service...');
    
    // Create server and transport
    server = new ArchScopeMcpServer();
    const transport = new StdioServerTransport();
    await server.connect(transport);
    
    // Log server information
    const serverInfo = server.getServerInfo();
    console.log('✅ ArchScope MCP Service started successfully!');
    console.log(`📋 Server: ${serverInfo.name} v${serverInfo.version}`);
    console.log(`🔗 ArchScope API: ${serverInfo.archscopeApiUrl}`);
    console.log(`📊 Log Level: ${serverInfo.logLevel}`);
    console.log('🎯 Available tools: pullTask, submitResult');
    console.log('⏳ Waiting for MCP client connections...');
    
    // Handle graceful shutdown
    const shutdown = async (signal: string): Promise<void> => {
      console.log(`\n🛑 Received ${signal}, shutting down gracefully...`);
      
      if (server) {
        try {
          await server.close();
          console.log('✅ Server closed successfully');
        } catch (error) {
          console.error('❌ Error closing server:', error);
        }
      }
      
      process.exit(0);
    };
    
    // Register signal handlers
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('❌ Uncaught Exception:', error);
      shutdown('uncaughtException').catch(() => process.exit(1));
    });
    
    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
      shutdown('unhandledRejection').catch(() => process.exit(1));
    });
    
  } catch (error) {
    console.error('❌ Failed to start ArchScope MCP Service:');
    
    if (error instanceof ConfigurationError) {
      console.error('🔧 Configuration Error:', error.message);
      console.error('💡 Please check your environment variables:');
      console.error('   - ARCHSCOPE_API_URL: Your ArchScope API base URL');
      console.error('   - ARCHSCOPE_API_TOKEN: Your ArchScope API bearer token');
      console.error('   - LOG_LEVEL (optional): debug, info, warn, error');
      console.error('   - HTTP_TIMEOUT (optional): Request timeout in milliseconds');
    } else {
      console.error('💥 Unexpected Error:', error);
    }
    
    if (server) {
      try {
        await server.close();
      } catch (closeError) {
        console.error('❌ Error closing server during cleanup:', closeError);
      }
    }
    
    process.exit(1);
  }
}

// Export server class for testing and programmatic use
export { ArchScopeMcpServer } from './server';
export * from './types/schemas';
export * from './utils/config';
export * from './utils/errors';

// Start the service if this file is run directly
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Fatal error in main:', error);
    process.exit(1);
  });
}
