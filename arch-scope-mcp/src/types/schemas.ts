/**
 * Zod schemas for ArchScope MCP Service
 * Defines input/output validation schemas for all tools
 */

import { z } from 'zod';

// ============================================================================
// Pull Task Schemas
// ============================================================================

/**
 * Schema for pullTask tool input parameters
 */
export const PullTaskInputSchema = z.object({
  workerId: z.string().min(1, 'workerId is required'),
  workerVersion: z.string().optional().default('1.0.0'),
  supportedTaskTypes: z.array(z.string()).optional(),
  maxConcurrentTasks: z.number().int().positive().optional().default(3),
});

export type PullTaskInput = z.infer<typeof PullTaskInputSchema>;

/**
 * Schema for repository information in task response
 */
export const RepositoryInfoSchema = z.object({
  cloneUrl: z.string().url(),
  commitId: z.string().regex(/^[a-f0-9]{40}$/, 'commitId must be a 40-character hex string'),
  branchName: z.string().optional(),
});

/**
 * Schema for task input data
 */
export const TaskInputDataSchema = z.object({
  schemaVersion: z.string(),
  repositoryInfo: RepositoryInfoSchema,
});

/**
 * Schema for successful pullTask response (when hasTask is true)
 */
export const PullTaskSuccessResponseSchema = z.object({
  hasTask: z.literal(true),
  taskId: z.union([z.string(), z.number()]).transform(val => String(val)),
  projectId: z.union([z.string(), z.number()]).transform(val => String(val)),
  taskType: z.string(),
  priority: z.number().int(),
  createdAt: z.string(),
  timeoutAt: z.string(),
  inputData: z.any().nullable().optional(),
  parameters: z.record(z.unknown()).optional(),
  message: z.any().nullable().optional(),
});

/**
 * Schema for pullTask response when no task is available
 */
export const PullTaskNoTaskResponseSchema = z.object({
  hasTask: z.literal(false),
  message: z.string().nullable().optional(),
  taskId: z.any().nullable().optional(),
  projectId: z.any().nullable().optional(),
  taskType: z.any().nullable().optional(),
  priority: z.any().nullable().optional(),
  inputData: z.any().nullable().optional(),
  createdAt: z.any().nullable().optional(),
  timeoutAt: z.any().nullable().optional(),
  parameters: z.any().nullable().optional(),
});

/**
 * Union schema for pullTask API response
 */
export const PullTaskResponseSchema = z.union([
  PullTaskSuccessResponseSchema,
  PullTaskNoTaskResponseSchema,
]);

export type PullTaskResponse = z.infer<typeof PullTaskResponseSchema>;

// ============================================================================
// Submit Result Schemas
// ============================================================================

/**
 * Schema for document result object
 */
export const DocumentResultSchema = z.object({
  documentType: z.string().min(1, 'documentType is required'),
  documentTitle: z.string().optional(),
  documentContent: z.string().optional(),
  filePath: z.string().optional(),
  status: z.enum(['SUCCESS', 'FAILED', 'SKIPPED']),
  errorMessage: z.string().optional(),
});

export type DocumentResult = z.infer<typeof DocumentResultSchema>;

/**
 * Schema for worker information
 */
export const WorkerInfoSchema = z.object({
  workerId: z.string(),
  workerVersion: z.string(),
});

export type WorkerInfo = z.infer<typeof WorkerInfoSchema>;

/**
 * Schema for submitResult tool input parameters
 */
export const SubmitResultInputSchema = z.object({
  taskId: z.string().min(1, 'taskId is required'),
  overallStatus: z.enum(['COMPLETED', 'FAILED', 'PARTIAL_SUCCESS']),
  commitId: z.string().regex(/^[a-f0-9]{40}$/, 'commitId must be a 40-character hex string').optional(),
  results: z.array(DocumentResultSchema).optional(),
  startTime: z.string().datetime().optional(),
  endTime: z.string().datetime().optional(),
  executionTimeMs: z.number().int().nonnegative().optional(),
  errorMessage: z.string().optional(),
  errorDetail: z.string().optional(),
  workerInfo: WorkerInfoSchema.optional(),
});

export type SubmitResultInput = z.infer<typeof SubmitResultInputSchema>;

/**
 * Schema for submitResult API response
 */
export const SubmitResultResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    taskId: z.union([z.string(), z.number()]).transform(val => String(val)),
    status: z.string(),
  }).optional(),
});

export type SubmitResultResponse = z.infer<typeof SubmitResultResponseSchema>;

// ============================================================================
// MCP Tool Result Schemas
// ============================================================================

/**
 * Schema for successful MCP tool result
 */
export const McpSuccessResultSchema = z.object({
  isError: z.literal(false),
  content: z.array(z.object({
    type: z.literal('text'),
    text: z.string(),
  })),
});

/**
 * Schema for error MCP tool result
 */
export const McpErrorResultSchema = z.object({
  isError: z.literal(true),
  content: z.array(z.object({
    type: z.literal('text'),
    text: z.string(),
  })),
});

/**
 * Union schema for MCP tool result
 */
export const McpToolResultSchema = z.union([
  McpSuccessResultSchema,
  McpErrorResultSchema,
]);

export type McpToolResult = z.infer<typeof McpToolResultSchema>;

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Create a successful MCP tool result with structured content
 */
export function createSuccessResult(data: unknown): McpToolResult {
  return {
    isError: false,
    content: [
      {
        type: 'text',
        text: JSON.stringify(data, null, 2),
      },
    ],
  };
}

/**
 * Create an error MCP tool result
 */
export function createErrorResult(message: string): McpToolResult {
  return {
    isError: true,
    content: [
      {
        type: 'text',
        text: message,
      },
    ],
  };
}
