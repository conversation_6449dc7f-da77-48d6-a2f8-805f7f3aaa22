package com.archscope.domain.service.impl;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.entity.Task;
import com.archscope.domain.model.TaskResultData;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.repository.TaskRepository;
import com.archscope.domain.service.DocumentStorageService;
import com.archscope.domain.service.DocumentVersionService;
import com.archscope.domain.service.LlmTaskService;
import com.archscope.domain.valueobject.DocumentType;
import com.archscope.domain.valueobject.TaskStatus;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * LLM任务服务实现
 */
@Service
public class LlmTaskServiceImpl implements LlmTaskService {

    private static final Logger log = LoggerFactory.getLogger(LlmTaskServiceImpl.class);

    private final TaskRepository taskRepository;
    private final ProjectRepository projectRepository;
    private final ObjectMapper objectMapper;
    private final DocumentStorageService documentStorageService;
    private final DocumentVersionService documentVersionService;

    public LlmTaskServiceImpl(TaskRepository taskRepository,
                             ProjectRepository projectRepository,
                             ObjectMapper objectMapper,
                             DocumentStorageService documentStorageService,
                             DocumentVersionService documentVersionService) {
        this.taskRepository = taskRepository;
        this.projectRepository = projectRepository;
        this.objectMapper = objectMapper;
        this.documentStorageService = documentStorageService;
        this.documentVersionService = documentVersionService;
    }

    // 默认任务超时时间(分钟)
    private static final int DEFAULT_TIMEOUT_MINUTES = 30;

    @Override
    @Transactional
    public Task createLlmTask(Long projectId, String repositoryUrl, String commitId, 
                              String branchName, String taskType, Integer priority) {
        log.info("创建LLM任务: projectId={}, taskType={}, commitId={}", projectId, taskType, commitId);

        // 验证项目存在
        projectRepository.findById(projectId)
                .orElseThrow(() -> new IllegalArgumentException("项目不存在: " + projectId));

        // 构建任务输入数据
        Map<String, Object> inputData = new HashMap<>();
        inputData.put("schemaVersion", "1.2");
        
        Map<String, Object> repositoryInfo = new HashMap<>();
        repositoryInfo.put("cloneUrl", repositoryUrl);
        repositoryInfo.put("commitId", commitId);
        if (branchName != null) {
            repositoryInfo.put("branchName", branchName);
        }
        inputData.put("repositoryInfo", repositoryInfo);

        // 序列化输入数据
        String parametersJson;
        try {
            parametersJson = objectMapper.writeValueAsString(inputData);
        } catch (JsonProcessingException e) {
            log.error("序列化任务参数失败", e);
            throw new RuntimeException("创建任务失败: 参数序列化错误", e);
        }

        // 创建任务
        Task task = Task.builder()
                .projectId(projectId)
                .taskType(taskType)
                .taskType(taskType)
                .status(TaskStatus.PENDING)
                .priority(priority != null ? priority : 5)
                .name("LLM代码分析任务")
                .description("使用LLM分析项目代码并生成文档")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .retryCount(0)
                .parameters(Collections.singletonMap("inputData", parametersJson))
                .build();

        return taskRepository.save(task);
    }

    @Override
    @Transactional
    public Optional<Task> pullNextTask() {
        log.debug("LLM工作节点请求任务");

        try {
            // 查找下一个待处理任务（使用FOR UPDATE SKIP LOCKED优化）
            Optional<Task> taskOpt = taskRepository.findNextPendingTask();

            if (!taskOpt.isPresent()) {
                log.debug("没有待处理任务");
                return Optional.empty();
            }

            Task task = taskOpt.get();

            // 生成工作节点ID
            String workerId = generateWorkerId();

            // 使用CAS操作锁定任务，避免并发冲突
            Integer expectedVersion = task.getTaskVersion() != null ? task.getTaskVersion() : 0;
            int updatedRows = taskRepository.lockTaskWithCAS(
                task.getId(), workerId, DEFAULT_TIMEOUT_MINUTES, expectedVersion);

            if (updatedRows > 0) {
                // 更新任务信息
                task.setStatus(TaskStatus.PROCESSING);
                task.setWorkerId(workerId);
                task.setProcessingStartedAt(LocalDateTime.now());
                task.setTimeoutAt(LocalDateTime.now().plusMinutes(DEFAULT_TIMEOUT_MINUTES));
                task.setTaskVersion(expectedVersion + 1);

                log.info("任务分配成功: taskId={}, workerId={}", task.getId(), workerId);
                return Optional.of(task);
            } else {
                log.debug("任务锁定失败，可能已被其他节点获取或版本冲突");
                return Optional.empty();
            }

        } catch (Exception e) {
            log.error("拉取任务失败", e);
            return Optional.empty();
        }
    }

    /**
     * 拉取下一个待处理任务并指定工作节点ID
     *
     * @param workerId 工作节点ID
     * @return 任务
     */
    @Override
    @Transactional
    public Optional<Task> pullNextTaskWithWorker(String workerId) {
        log.debug("LLM工作节点请求任务: workerId={}", workerId);

        try {
            // 在查询任务之前，先处理超时任务
            handleTimeoutTasksBeforePull();

            // 查找下一个待处理任务（使用FOR UPDATE SKIP LOCKED优化）
            Optional<Task> taskOpt = taskRepository.findNextPendingTask();

            if (!taskOpt.isPresent()) {
                log.debug("没有待处理任务");
                return Optional.empty();
            }

            Task task = taskOpt.get();

            // 使用CAS操作锁定任务，避免并发冲突
            Integer expectedVersion = task.getTaskVersion() != null ? task.getTaskVersion() : 0;
            int updatedRows = taskRepository.lockTaskWithCAS(
                task.getId(), workerId, DEFAULT_TIMEOUT_MINUTES, expectedVersion);

            if (updatedRows > 0) {
                // 更新任务信息
                task.setStatus(TaskStatus.PROCESSING);
                task.setWorkerId(workerId);
                task.setProcessingStartedAt(LocalDateTime.now());
                task.setTimeoutAt(LocalDateTime.now().plusMinutes(DEFAULT_TIMEOUT_MINUTES));
                task.setTaskVersion(expectedVersion + 1);

                log.info("任务分配成功: taskId={}, workerId={}", task.getId(), workerId);
                return Optional.of(task);
            } else {
                log.debug("任务锁定失败，可能已被其他节点获取或版本冲突");
                return Optional.empty();
            }

        } catch (Exception e) {
            log.error("拉取任务失败", e);
            return Optional.empty();
        }
    }

    @Override
    @Transactional
    public boolean deliverTaskResult(Long taskId, String status, String documentContent, String errorMessage) {
        log.info("接收LLM任务结果: taskId={}, status={}", taskId, status);

        try {
            Task task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));

            // 验证任务状态
            if (task.getStatus() != TaskStatus.PROCESSING) {
                log.warn("任务状态不正确，无法交付结果: taskId={}, currentStatus={}", taskId, task.getStatus());
                return false;
            }

            // 更新任务状态和结果
            if ("SUCCESS".equalsIgnoreCase(status)) {
                task.setStatus(TaskStatus.COMPLETED);
                task.setResult(documentContent);
                task.setProgress(100);
                log.info("任务执行成功: taskId={}", taskId);
            } else {
                task.setStatus(TaskStatus.FAILED);
                task.setErrorLog(errorMessage);
                log.warn("任务执行失败: taskId={}, error={}", taskId, errorMessage);
            }

            task.setUpdatedAt(LocalDateTime.now());
            taskRepository.save(task);

            // TODO: 如果任务成功，触发文档生成和存储流程
            if (TaskStatus.COMPLETED.equals(task.getStatus()) && documentContent != null) {
                handleSuccessfulTaskResult(task, documentContent);
            }

            return true;

        } catch (Exception e) {
            log.error("处理任务结果失败: taskId=" + taskId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public int handleTimeoutTasks() {
        log.debug("检查超时任务");

        // 查找所有进行中的任务
        List<Task> processingTasks = taskRepository.findAllByStatus(TaskStatus.PROCESSING);
        int timeoutCount = 0;

        for (Task task : processingTasks) {
            if (isTaskTimeout(task.getId())) {
                log.warn("任务超时，重置为待处理状态: taskId={}", task.getId());
                unlockTask(task.getId());
                timeoutCount++;
            }
        }

        if (timeoutCount > 0) {
            log.info("处理了 {} 个超时任务", timeoutCount);
        }

        return timeoutCount;
    }

    @Override
    public Optional<String> getTaskInputData(Long taskId) {
        return taskRepository.findById(taskId)
                .map(task -> {
                    Map<String, Object> parameters = task.getParameters();
                    if (parameters != null && parameters.containsKey("inputData")) {
                        return (String) parameters.get("inputData");
                    }
                    return null;
                });
    }

    @Override
    @Transactional
    public boolean lockTask(Long taskId, int timeoutMinutes) {
        try {
            Task task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));

            if (task.getStatus() != TaskStatus.PENDING) {
                log.warn("任务状态不是PENDING，无法锁定: taskId={}, status={}", taskId, task.getStatus());
                return false;
            }

            // 设置任务为处理中状态，并设置超时时间
            task.setStatus(TaskStatus.PROCESSING);
            task.setUpdatedAt(LocalDateTime.now());
            
            // 在parameters中存储超时时间
            Map<String, Object> parameters = task.getParameters();
            if (parameters == null) {
                parameters = new HashMap<>();
            }
            parameters.put("timeoutAt", LocalDateTime.now().plusMinutes(timeoutMinutes));
            task.setParameters(parameters);

            taskRepository.save(task);
            log.debug("任务已锁定: taskId={}, timeoutMinutes={}", taskId, timeoutMinutes);
            return true;

        } catch (Exception e) {
            log.error("锁定任务失败: taskId=" + taskId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean unlockTask(Long taskId) {
        try {
            Task task = taskRepository.findById(taskId)
                    .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));

            if (task.getStatus() != TaskStatus.PROCESSING) {
                log.warn("任务状态不是PROCESSING，无法解锁: taskId={}, status={}", taskId, task.getStatus());
                return false;
            }

            // 重置任务状态为待处理
            task.setStatus(TaskStatus.PENDING);
            task.setUpdatedAt(LocalDateTime.now());
            
            // 清除超时时间
            Map<String, Object> parameters = task.getParameters();
            if (parameters != null) {
                parameters.remove("timeoutAt");
                task.setParameters(parameters);
            }

            taskRepository.save(task);
            log.debug("任务已解锁: taskId={}", taskId);
            return true;

        } catch (Exception e) {
            log.error("解锁任务失败: taskId=" + taskId, e);
            return false;
        }
    }

    @Override
    public boolean isTaskTimeout(Long taskId) {
        return taskRepository.findById(taskId)
                .map(task -> {
                    Map<String, Object> parameters = task.getParameters();
                    if (parameters != null && parameters.containsKey("timeoutAt")) {
                        LocalDateTime timeoutAt = (LocalDateTime) parameters.get("timeoutAt");
                        return LocalDateTime.now().isAfter(timeoutAt);
                    }
                    return false;
                })
                .orElse(false);
    }

    @Override
    @Transactional
    public boolean lockTaskWithWorker(Long taskId, String workerId, int timeoutMinutes) {
        log.debug("锁定任务并指定工作节点: taskId={}, workerId={}", taskId, workerId);

        try {
            LocalDateTime timeoutAt = LocalDateTime.now().plusMinutes(timeoutMinutes);

            int updatedRows = taskRepository.updateTaskToProcessing(taskId, workerId, timeoutAt);

            if (updatedRows > 0) {
                log.info("任务锁定成功: taskId={}, workerId={}", taskId, workerId);
                return true;
            } else {
                log.debug("任务锁定失败，可能已被其他节点锁定: taskId={}", taskId);
                return false;
            }

        } catch (Exception e) {
            log.error("任务锁定失败: taskId={}", taskId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean completeTaskWithResult(Long taskId, String overallStatus, String results, Long executionTimeMs) {
        log.info("完成任务: taskId={}, overallStatus={}", taskId, overallStatus);

        try {
            // 更新数据库
            int updatedRows = taskRepository.completeTaskWithResult(taskId, overallStatus, results, executionTimeMs);

            if (updatedRows > 0) {
                // 释放Redis锁
                unlockTask(taskId);
                log.info("任务完成: taskId={}, status={}", taskId, overallStatus);
                return true;
            } else {
                log.warn("任务完成失败，可能任务状态不正确: taskId={}", taskId);
                return false;
            }

        } catch (Exception e) {
            log.error("完成任务失败: taskId={}", taskId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean failTaskWithError(Long taskId, String errorDetail, Long executionTimeMs) {
        log.info("标记任务失败: taskId={}", taskId);

        try {
            // 更新数据库
            int updatedRows = taskRepository.failTaskWithError(taskId, errorDetail, executionTimeMs);

            if (updatedRows > 0) {
                // 释放Redis锁
                unlockTask(taskId);
                log.info("任务已标记为失败: taskId={}", taskId);
                return true;
            } else {
                log.warn("标记任务失败失败，可能任务状态不正确: taskId={}", taskId);
                return false;
            }

        } catch (Exception e) {
            log.error("标记任务失败失败: taskId={}", taskId, e);
            return false;
        }
    }

    /**
     * 处理成功的任务结果
     */
    private void handleSuccessfulTaskResult(Task task, String resultsJson) {
        log.info("处理成功的任务结果: taskId={}, resultsLength={}",
                task.getId(), resultsJson != null ? resultsJson.length() : 0);

        if (resultsJson == null || resultsJson.trim().isEmpty()) {
            log.warn("任务结果为空，跳过处理: taskId={}", task.getId());
            return;
        }

        try {
            // 解析JSON结果为TaskResultData
            TaskResultData taskResultData = parseTaskResults(resultsJson);

            if (taskResultData == null || taskResultData.getResults() == null || taskResultData.getResults().isEmpty()) {
                log.warn("解析任务结果为空: taskId={}", task.getId());
                return;
            }

            // 处理每个文档结果
            for (TaskResultData.DocumentResult result : taskResultData.getResults()) {
                if ("SUCCESS".equals(result.getStatus())) {
                    processDocumentResult(task, result, taskResultData.getCommitId());
                } else {
                    log.warn("跳过失败的文档: taskId={}, docType={}, error={}",
                            task.getId(), result.getDocumentType(), result.getErrorMessage());
                }
            }

            log.info("任务结果处理完成: taskId={}, 成功处理文档数量={}",
                    task.getId(), taskResultData.getResults().stream().mapToInt(r -> "SUCCESS".equals(r.getStatus()) ? 1 : 0).sum());

        } catch (Exception e) {
            log.error("处理任务结果失败: taskId={}", task.getId(), e);
        }
    }

    /**
     * 解析JSON格式的任务结果
     */
    private TaskResultData parseTaskResults(String resultsJson) throws JsonProcessingException {
        if (resultsJson == null || resultsJson.trim().isEmpty()) {
            return null;
        }

        // 尝试直接解析为TaskResultData格式
        try {
            return objectMapper.readValue(resultsJson, TaskResultData.class);
        } catch (JsonProcessingException e) {
            log.debug("直接解析TaskResultData失败，尝试解析为List格式: {}", e.getMessage());
        }

        // 如果直接解析失败，尝试解析为List<DocumentResult>格式
        try {
            TypeReference<List<TaskResultData.DocumentResult>> typeRef = new TypeReference<List<TaskResultData.DocumentResult>>() {};
            List<TaskResultData.DocumentResult> results = objectMapper.readValue(resultsJson, typeRef);
            return TaskResultData.builder()
                    .results(results)
                    .build();
        } catch (JsonProcessingException e) {
            log.error("解析任务结果JSON失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 处理单个文档结果
     */
    private void processDocumentResult(Task task, TaskResultData.DocumentResult result, String commitId) {
        log.info("处理文档结果: taskId={}, docType={}, title={}",
                task.getId(), result.getDocumentType(), result.getDocumentTitle());

        try {
            // 解析文档类型
            DocumentType docType = parseDocumentType(result.getDocumentType());
            if (docType == null) {
                log.warn("不支持的文档类型: {}", result.getDocumentType());
                return;
            }

            // 生成版本标签
            String versionTag = documentVersionService.generateVersionTag(task.getProjectId(), docType);

            // 存储文档内容到文件系统
            String contentPath = documentStorageService.storeDocument(
                    task.getProjectId(),
                    result.getDocumentType(),
                    result.getDocumentContent(),
                    versionTag
            );

            // 创建DocumentVersion记录
            DocumentVersion documentVersion = DocumentVersion.builder()
                    .projectId(task.getProjectId())
                    .commitId(commitId != null ? commitId : "unknown")
                    .contentPath(contentPath)
                    .timestamp(LocalDateTime.now())
                    .docType(docType)
                    .versionTag(versionTag)
                    .title(result.getDocumentTitle())
                    .description("LLM自动生成的文档")
                    .author("LLM")
                    .lastModified(LocalDateTime.now())
                    .isPublished(true)
                    .status("PUBLISHED")
                    .build();

            documentVersionService.createDocumentVersion(documentVersion);

            log.info("文档处理成功: taskId={}, docType={}, version={}, path={}",
                    task.getId(), result.getDocumentType(), versionTag, contentPath);

        } catch (Exception e) {
            log.error("处理文档结果失败: taskId={}, docType={}",
                    task.getId(), result.getDocumentType(), e);
        }
    }

    /**
     * 解析文档类型字符串为DocumentType枚举
     */
    private DocumentType parseDocumentType(String docTypeStr) {
        if (docTypeStr == null || docTypeStr.trim().isEmpty()) {
            return null;
        }

        try {
            return DocumentType.valueOf(docTypeStr.toUpperCase());
        } catch (IllegalArgumentException e) {
            // 尝试一些常见的映射
            switch (docTypeStr.toUpperCase()) {
                case "README":
                case "PRODUCT_INTRO":
                    return DocumentType.PRODUCT_INTRO;
                case "ARCHITECTURE":
                case "ARCH":
                    return DocumentType.ARCHITECTURE;
                case "API":
                case "API_DOC":
                    return DocumentType.API;
                case "USER_MANUAL":
                case "MANUAL":
                    return DocumentType.USER_MANUAL;
                case "EXTENSION":
                case "EXT":
                    return DocumentType.EXTENSION;
                case "LLMS_TXT":
                case "LLM":
                    return DocumentType.LLMS_TXT;
                default:
                    log.warn("无法映射文档类型: {}", docTypeStr);
                    return null;
            }
        }
    }

    /**
     * 在拉取任务前处理超时任务
     * 自动重置超时的PROCESSING任务为PENDING状态，使其可以被重新拉取
     */
    private void handleTimeoutTasksBeforePull() {
        try {
            log.debug("检查并处理超时任务");

            // 查找所有超时的PROCESSING任务
            List<Task> timeoutTasks = taskRepository.findTimeoutProcessingTasks();

            if (timeoutTasks.isEmpty()) {
                log.debug("没有发现超时任务");
                return;
            }

            log.info("发现 {} 个超时任务，开始重置为PENDING状态", timeoutTasks.size());

            int resetCount = 0;
            for (Task task : timeoutTasks) {
                try {
                    log.debug("重置超时任务: taskId={}, workerId={}, timeoutAt={}",
                            task.getId(), task.getWorkerId(), task.getTimeoutAt());

                    // 重置任务状态为PENDING
                    int updatedRows = taskRepository.resetTimeoutTask(task.getId());

                    if (updatedRows > 0) {
                        resetCount++;
                        log.debug("超时任务重置成功: taskId={}", task.getId());
                    } else {
                        log.warn("超时任务重置失败: taskId={}", task.getId());
                    }

                } catch (Exception e) {
                    log.error("重置超时任务失败: taskId=" + task.getId(), e);
                }
            }

            if (resetCount > 0) {
                log.info("成功重置 {} 个超时任务为PENDING状态", resetCount);
            }

        } catch (Exception e) {
            log.error("处理超时任务时发生异常", e);
        }
    }

    /**
     * 生成工作节点ID
     */
    private String generateWorkerId() {
        return "worker-" + System.currentTimeMillis() + "-" +
               Thread.currentThread().hashCode();
    }


}
