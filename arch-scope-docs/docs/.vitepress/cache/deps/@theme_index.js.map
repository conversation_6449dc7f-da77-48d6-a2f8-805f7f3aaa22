{"version": 3, "sources": ["../../../../node_modules/vitepress/dist/client/theme-default/index.js", "../../../../node_modules/vitepress/dist/client/theme-default/without-fonts.js", "../../../../node_modules/vitepress/dist/client/theme-default/composables/layout.js", "../../../../node_modules/vitepress/dist/client/shared.js", "../../../../node_modules/vitepress/dist/client/theme-default/support/utils.js", "../../../../node_modules/vitepress/dist/client/theme-default/composables/data.js", "../../../../node_modules/vitepress/dist/client/theme-default/support/sidebar.js", "../../../../node_modules/vitepress/dist/client/theme-default/composables/outline.js", "../../../../node_modules/vitepress/dist/client/theme-default/composables/sidebar.js"], "sourcesContent": ["import './styles/fonts.css';\nexport * from './without-fonts';\nexport { default as default } from './without-fonts';\n", "import './styles/vars.css';\nimport './styles/base.css';\nimport './styles/icons.css';\nimport './styles/utils.css';\nimport './styles/components/custom-block.css';\nimport './styles/components/vp-code.css';\nimport './styles/components/vp-code-group.css';\nimport './styles/components/vp-doc.css';\nimport './styles/components/vp-sponsor.css';\nimport VPBadge from './components/VPBadge.vue';\nimport Layout from './Layout.vue';\nexport { default as VPBadge } from './components/VPBadge.vue';\nexport { default as VPButton } from './components/VPButton.vue';\nexport { default as VPDocAsideSponsors } from './components/VPDocAsideSponsors.vue';\nexport { default as VPFeatures } from './components/VPFeatures.vue';\nexport { default as VPHomeContent } from './components/VPHomeContent.vue';\nexport { default as VPHomeFeatures } from './components/VPHomeFeatures.vue';\nexport { default as VPHomeHero } from './components/VPHomeHero.vue';\nexport { default as VPHomeSponsors } from './components/VPHomeSponsors.vue';\nexport { default as VPImage } from './components/VPImage.vue';\nexport { default as VPLink } from './components/VPLink.vue';\nexport { default as VPNavBarSearch } from './components/VPNavBarSearch.vue';\nexport { default as VPSocialLink } from './components/VPSocialLink.vue';\nexport { default as VPSocialLinks } from './components/VPSocialLinks.vue';\nexport { default as VPSponsors } from './components/VPSponsors.vue';\nexport { default as VPTeamMembers } from './components/VPTeamMembers.vue';\nexport { default as VPTeamPage } from './components/VPTeamPage.vue';\nexport { default as VPTeamPageSection } from './components/VPTeamPageSection.vue';\nexport { default as VPTeamPageTitle } from './components/VPTeamPageTitle.vue';\nexport { useLayout } from './composables/layout';\nconst theme = {\n    Layout,\n    enhanceApp: ({ app }) => {\n        app.component('Badge', VPBadge);\n    }\n};\nexport default theme;\n", "import { inBrowser, onContentUpdated, useRoute } from 'vitepress';\nimport { computed, shallowReadonly, shallowRef, watch } from 'vue';\nimport { getSidebar, getSidebarGroups } from '../support/sidebar';\nimport { useData } from './data';\nimport { getHeaders } from './outline';\nimport { useCloseSidebarOnEscape } from './sidebar';\nconst headers = shallowRef([]);\nconst sidebar = shallowRef([]);\nconst is960 = shallowRef(false);\nexport function useLayout() {\n    const { frontmatter, theme } = useData();\n    const isHome = computed(() => {\n        return !!(frontmatter.value.isHome ?? frontmatter.value.layout === 'home');\n    });\n    const hasSidebar = computed(() => {\n        return (frontmatter.value.sidebar !== false &&\n            sidebar.value.length > 0 &&\n            !isHome.value);\n    });\n    const isSidebarEnabled = computed(() => hasSidebar.value && is960.value);\n    const sidebarGroups = computed(() => {\n        return hasSidebar.value ? getSidebarGroups(sidebar.value) : [];\n    });\n    const hasAside = computed(() => {\n        if (isHome.value)\n            return false;\n        if (frontmatter.value.aside != null)\n            return !!frontmatter.value.aside;\n        return theme.value.aside !== false;\n    });\n    const leftAside = computed(() => {\n        if (!hasAside.value)\n            return false;\n        return frontmatter.value.aside == null\n            ? theme.value.aside === 'left'\n            : frontmatter.value.aside === 'left';\n    });\n    const hasLocalNav = computed(() => {\n        return headers.value.length > 0;\n    });\n    return {\n        isHome,\n        sidebar: shallowReadonly(sidebar),\n        sidebarGroups,\n        hasSidebar,\n        isSidebarEnabled,\n        hasAside,\n        leftAside,\n        headers: shallowReadonly(headers),\n        hasLocalNav\n    };\n}\nexport function registerWatchers({ closeSidebar }) {\n    const { frontmatter, page, theme } = useData();\n    watch(() => [page.value.relativePath, theme.value.sidebar], ([relativePath, sidebarConfig]) => {\n        const newSidebar = sidebarConfig\n            ? getSidebar(sidebarConfig, relativePath)\n            : [];\n        if (JSON.stringify(newSidebar) !== JSON.stringify(sidebar.value)) {\n            sidebar.value = newSidebar;\n        }\n    }, { immediate: true, deep: true, flush: 'sync' });\n    onContentUpdated(() => {\n        headers.value = getHeaders(frontmatter.value.outline ?? theme.value.outline);\n    });\n    if (inBrowser) {\n        is960.value = window.innerWidth >= 960;\n        window.addEventListener('resize', () => {\n            is960.value = window.innerWidth >= 960;\n        }, { passive: true });\n    }\n    const route = useRoute();\n    watch(() => route.path, closeSidebar);\n    useCloseSidebarOnEscape(closeSidebar);\n}\n", "export const EXTERNAL_URL_RE = /^(?:[a-z]+:|\\/\\/)/i;\nexport const APPEARANCE_KEY = 'vitepress-theme-appearance';\nexport const VP_SOURCE_KEY = '[VP_SOURCE]';\nconst UnpackStackView = Symbol('stack-view:unpack');\nconst HASH_RE = /#.*$/;\nconst HASH_OR_QUERY_RE = /[?#].*$/;\nconst INDEX_OR_EXT_RE = /(?:(^|\\/)index)?\\.(?:md|html)$/;\nexport const inBrowser = typeof document !== 'undefined';\nexport const notFoundPageData = {\n    relativePath: '404.md',\n    filePath: '',\n    title: '404',\n    description: 'Not Found',\n    headers: [],\n    frontmatter: { sidebar: false, layout: 'page' },\n    lastUpdated: 0,\n    isNotFound: true\n};\nexport function isActive(currentPath, matchPath, asRegex = false) {\n    if (matchPath === undefined) {\n        return false;\n    }\n    currentPath = normalize(`/${currentPath}`);\n    if (asRegex) {\n        return new RegExp(matchPath).test(currentPath);\n    }\n    if (normalize(matchPath) !== currentPath) {\n        return false;\n    }\n    const hashMatch = matchPath.match(HASH_RE);\n    if (hashMatch) {\n        return (inBrowser ? location.hash : '') === hashMatch[0];\n    }\n    return true;\n}\nexport function normalize(path) {\n    return decodeURI(path)\n        .replace(HASH_OR_QUERY_RE, '')\n        .replace(INDEX_OR_EXT_RE, '$1');\n}\nexport function isExternal(path) {\n    return EXTERNAL_URL_RE.test(path);\n}\nexport function getLocaleForPath(siteData, relativePath) {\n    return (Object.keys(siteData?.locales || {}).find((key) => key !== 'root' &&\n        !isExternal(key) &&\n        isActive(relativePath, `^/${key}/`, true)) || 'root');\n}\n/**\n * this merges the locales data to the main data by the route\n */\nexport function resolveSiteDataByRoute(siteData, relativePath) {\n    const localeIndex = getLocaleForPath(siteData, relativePath);\n    const { label, link, ...localeConfig } = siteData.locales[localeIndex] ?? {};\n    Object.assign(localeConfig, { localeIndex });\n    const additionalConfigs = resolveAdditionalConfig(siteData, relativePath);\n    if (inBrowser && import.meta.env?.DEV) {\n        ;\n        localeConfig[VP_SOURCE_KEY] = `locale config (${localeIndex})`;\n        reportConfigLayers(relativePath, [\n            ...additionalConfigs,\n            localeConfig,\n            siteData\n        ]);\n    }\n    const topLayer = {\n        head: mergeHead(siteData.head ?? [], localeConfig.head ?? [], ...additionalConfigs.map((data) => data.head ?? []).reverse())\n    };\n    return stackView(topLayer, ...additionalConfigs, localeConfig, siteData);\n}\n/**\n * Create the page title string based on config.\n */\nexport function createTitle(siteData, pageData) {\n    const title = pageData.title || siteData.title;\n    const template = pageData.titleTemplate ?? siteData.titleTemplate;\n    if (typeof template === 'string' && template.includes(':title')) {\n        return template.replace(/:title/g, title);\n    }\n    const templateString = createTitleTemplate(siteData.title, template);\n    if (title === templateString.slice(3)) {\n        return title;\n    }\n    return `${title}${templateString}`;\n}\nfunction createTitleTemplate(siteTitle, template) {\n    if (template === false) {\n        return '';\n    }\n    if (template === true || template === undefined) {\n        return ` | ${siteTitle}`;\n    }\n    if (siteTitle === template) {\n        return '';\n    }\n    return ` | ${template}`;\n}\nexport function mergeHead(...headArrays) {\n    const merged = [];\n    const metaKeyMap = new Map();\n    for (const current of headArrays) {\n        for (const tag of current) {\n            const [type, attrs] = tag;\n            const keyAttr = Object.entries(attrs)[0];\n            if (type !== 'meta' || !keyAttr) {\n                merged.push(tag);\n                continue;\n            }\n            const key = `${keyAttr[0]}=${keyAttr[1]}`;\n            const existingIndex = metaKeyMap.get(key);\n            if (existingIndex != null) {\n                merged[existingIndex] = tag; // replace existing tag\n            }\n            else {\n                metaKeyMap.set(key, merged.length);\n                merged.push(tag);\n            }\n        }\n    }\n    return merged;\n}\n// https://github.com/rollup/rollup/blob/fec513270c6ac350072425cc045db367656c623b/src/utils/sanitizeFileName.ts\nconst INVALID_CHAR_REGEX = /[\\u0000-\\u001F\"#$&*+,:;<=>?[\\]^`{|}\\u007F]/g;\nconst DRIVE_LETTER_REGEX = /^[a-z]:/i;\nexport function sanitizeFileName(name) {\n    const match = DRIVE_LETTER_REGEX.exec(name);\n    const driveLetter = match ? match[0] : '';\n    return (driveLetter +\n        name\n            .slice(driveLetter.length)\n            .replace(INVALID_CHAR_REGEX, '_')\n            .replace(/(^|\\/)_+(?=[^/]*$)/, '$1'));\n}\nexport function slash(p) {\n    return p.replace(/\\\\/g, '/');\n}\nconst KNOWN_EXTENSIONS = new Set();\nexport function treatAsHtml(filename) {\n    if (KNOWN_EXTENSIONS.size === 0) {\n        const extraExts = (typeof process === 'object' && process.env?.VITE_EXTRA_EXTENSIONS) ||\n            import.meta.env?.VITE_EXTRA_EXTENSIONS ||\n            '';\n        ('3g2,3gp,aac,ai,apng,au,avif,bin,bmp,cer,class,conf,crl,css,csv,dll,' +\n            'doc,eps,epub,exe,gif,gz,ics,ief,jar,jpe,jpeg,jpg,js,json,jsonld,m4a,' +\n            'man,mid,midi,mjs,mov,mp2,mp3,mp4,mpe,mpeg,mpg,mpp,oga,ogg,ogv,ogx,' +\n            'opus,otf,p10,p7c,p7m,p7s,pdf,png,ps,qt,roff,rtf,rtx,ser,svg,t,tif,' +\n            'tiff,tr,ts,tsv,ttf,txt,vtt,wav,weba,webm,webp,woff,woff2,xhtml,xml,' +\n            'yaml,yml,zip' +\n            (extraExts && typeof extraExts === 'string' ? ',' + extraExts : ''))\n            .split(',')\n            .forEach((ext) => KNOWN_EXTENSIONS.add(ext));\n    }\n    const ext = filename.split('.').pop();\n    return ext == null || !KNOWN_EXTENSIONS.has(ext.toLowerCase());\n}\n// https://github.com/sindresorhus/escape-string-regexp/blob/ba9a4473850cb367936417e97f1f2191b7cc67dd/index.js\nexport function escapeRegExp(str) {\n    return str.replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&').replace(/-/g, '\\\\x2d');\n}\n/**\n * @internal\n */\nexport function escapeHtml(str) {\n    return str\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;')\n        .replace(/\"/g, '&quot;')\n        .replace(/&(?![\\w#]+;)/g, '&amp;');\n}\nfunction resolveAdditionalConfig({ additionalConfig }, path) {\n    if (additionalConfig === undefined)\n        return [];\n    if (typeof additionalConfig === 'function')\n        return additionalConfig(path) ?? [];\n    const configs = [];\n    const segments = path.split('/').slice(0, -1); // remove file name\n    while (segments.length) {\n        const key = `/${segments.join('/')}/`;\n        configs.push(additionalConfig[key]);\n        segments.pop();\n    }\n    configs.push(additionalConfig['/']);\n    return configs.filter((config) => config !== undefined);\n}\n// This helps users to understand which configuration files are active\nfunction reportConfigLayers(path, layers) {\n    const summaryTitle = `Config Layers for ${path}:`;\n    const summary = layers.map((c, i, arr) => {\n        const n = i + 1;\n        if (n === arr.length)\n            return `${n}. .vitepress/config (root)`;\n        return `${n}. ${c?.[VP_SOURCE_KEY] ?? '(Unknown Source)'}`;\n    });\n    console.debug([summaryTitle, ''.padEnd(summaryTitle.length, '='), ...summary].join('\\n'));\n}\n/**\n * Creates a deep, merged view of multiple objects without mutating originals.\n * Returns a readonly proxy behaving like a merged object of the input objects.\n * Layers are merged in descending precedence, i.e. earlier layer is on top.\n */\nexport function stackView(..._layers) {\n    const layers = _layers.filter((layer) => isObject(layer));\n    if (layers.length <= 1)\n        return _layers[0];\n    const allKeys = new Set(layers.flatMap((layer) => Reflect.ownKeys(layer)));\n    const allKeysArray = [...allKeys];\n    return new Proxy({}, {\n        // TODO: optimize for performance, this is a hot path\n        get(_, prop) {\n            if (prop === UnpackStackView)\n                return layers;\n            return stackView(...layers\n                .map((layer) => layer[prop])\n                .filter((v) => v !== undefined));\n        },\n        set() {\n            throw new Error('StackView is read-only and cannot be mutated.');\n        },\n        has(_, prop) {\n            return allKeys.has(prop);\n        },\n        ownKeys() {\n            return allKeysArray;\n        },\n        getOwnPropertyDescriptor(_, prop) {\n            for (const layer of layers) {\n                const descriptor = Object.getOwnPropertyDescriptor(layer, prop);\n                if (descriptor)\n                    return descriptor;\n            }\n        }\n    });\n}\nstackView.unpack = function (obj) {\n    return obj?.[UnpackStackView];\n};\nexport function isObject(value) {\n    return Object.prototype.toString.call(value) === '[object Object]';\n}\n", "import { withBase } from 'vitepress';\nimport { isExternal, treatAsHtml } from '../../shared';\nimport { useData } from '../composables/data';\nexport function throttleAndDebounce(fn, delay) {\n    let timeoutId;\n    let called = false;\n    return () => {\n        if (timeoutId)\n            clearTimeout(timeoutId);\n        if (!called) {\n            fn();\n            (called = true) && setTimeout(() => (called = false), delay);\n        }\n        else\n            timeoutId = setTimeout(fn, delay);\n    };\n}\nexport function ensureStartingSlash(path) {\n    return path.startsWith('/') ? path : `/${path}`;\n}\nexport function normalizeLink(url) {\n    const { pathname, search, hash, protocol } = new URL(url, 'http://a.com');\n    if (isExternal(url) ||\n        url.startsWith('#') ||\n        !protocol.startsWith('http') ||\n        !treatAsHtml(pathname))\n        return url;\n    const { site } = useData();\n    const normalizedPath = pathname.endsWith('/') || pathname.endsWith('.html')\n        ? url\n        : url.replace(/(?:(^\\.+)\\/)?.*$/, `$1${pathname.replace(/(\\.md)?$/, site.value.cleanUrls ? '' : '.html')}${search}${hash}`);\n    return withBase(normalizedPath);\n}\n", "import { useData as useData$ } from 'vitepress';\nexport const useData = useData$;\n", "import { isActive } from '../../shared';\nimport { ensureStartingSlash } from './utils';\n/**\n * Get the `Sidebar` from sidebar option. This method will ensure to get correct\n * sidebar config from `MultiSideBarConfig` with various path combinations such\n * as matching `guide/` and `/guide/`. If no matching config was found, it will\n * return empty array.\n */\nexport function getSidebar(_sidebar, path) {\n    if (Array.isArray(_sidebar))\n        return addBase(_sidebar);\n    if (_sidebar == null)\n        return [];\n    path = ensureStartingSlash(path);\n    const dir = Object.keys(_sidebar)\n        .sort((a, b) => {\n        return b.split('/').length - a.split('/').length;\n    })\n        .find((dir) => {\n        // make sure the multi sidebar key starts with slash too\n        return path.startsWith(ensureStartingSlash(dir));\n    });\n    const sidebar = dir ? _sidebar[dir] : [];\n    return Array.isArray(sidebar)\n        ? addBase(sidebar)\n        : addBase(sidebar.items, sidebar.base);\n}\n/**\n * Get or generate sidebar group from the given sidebar items.\n */\nexport function getSidebarGroups(sidebar) {\n    const groups = [];\n    let lastGroupIndex = 0;\n    for (const index in sidebar) {\n        const item = sidebar[index];\n        if (item.items) {\n            lastGroupIndex = groups.push(item);\n            continue;\n        }\n        if (!groups[lastGroupIndex]) {\n            groups.push({ items: [] });\n        }\n        groups[lastGroupIndex].items.push(item);\n    }\n    return groups;\n}\nexport function getFlatSideBarLinks(sidebar) {\n    const links = [];\n    function recursivelyExtractLinks(items) {\n        for (const item of items) {\n            if (item.text && item.link) {\n                links.push({\n                    text: item.text,\n                    link: item.link,\n                    docFooterText: item.docFooterText\n                });\n            }\n            if (item.items) {\n                recursivelyExtractLinks(item.items);\n            }\n        }\n    }\n    recursivelyExtractLinks(sidebar);\n    return links;\n}\n/**\n * Check if the given sidebar item contains any active link.\n */\nexport function hasActiveLink(path, items) {\n    if (Array.isArray(items)) {\n        return items.some((item) => hasActiveLink(path, item));\n    }\n    return isActive(path, items.link)\n        ? true\n        : items.items\n            ? hasActiveLink(path, items.items)\n            : false;\n}\nfunction addBase(items, _base) {\n    return [...items].map((_item) => {\n        const item = { ..._item };\n        const base = item.base || _base;\n        if (base && item.link)\n            item.link = base + item.link.replace(/^\\//, base.endsWith('/') ? '' : '/');\n        if (item.items)\n            item.items = addBase(item.items, base);\n        return item;\n    });\n}\n", "import { getScrollOffset } from 'vitepress';\nimport { onMounted, onUnmounted, onUpdated } from 'vue';\nimport { throttleAndDebounce } from '../support/utils';\nimport { useAside } from './aside';\nconst ignoreRE = /\\b(?:VPBadge|header-anchor|footnote-ref|ignore-header)\\b/;\n// cached list of anchor elements from resolveHeaders\nconst resolvedHeaders = [];\nexport function resolveTitle(theme) {\n    return ((typeof theme.outline === 'object' &&\n        !Array.isArray(theme.outline) &&\n        theme.outline.label) ||\n        theme.outlineTitle ||\n        'On this page');\n}\nexport function getHeaders(range) {\n    const headers = [\n        ...document.querySelectorAll('.VPDoc :where(h1,h2,h3,h4,h5,h6)')\n    ]\n        .filter((el) => el.id && el.hasChildNodes())\n        .map((el) => {\n        const level = Number(el.tagName[1]);\n        return {\n            element: el,\n            title: serializeHeader(el),\n            link: '#' + el.id,\n            level\n        };\n    });\n    return resolveHeaders(headers, range);\n}\nfunction serializeHeader(h) {\n    let ret = '';\n    for (const node of h.childNodes) {\n        if (node.nodeType === 1) {\n            if (ignoreRE.test(node.className))\n                continue;\n            ret += node.textContent;\n        }\n        else if (node.nodeType === 3) {\n            ret += node.textContent;\n        }\n    }\n    return ret.trim();\n}\nexport function resolveHeaders(headers, range) {\n    if (range === false) {\n        return [];\n    }\n    const levelsRange = (typeof range === 'object' && !Array.isArray(range)\n        ? range.level\n        : range) || 2;\n    const [high, low] = typeof levelsRange === 'number'\n        ? [levelsRange, levelsRange]\n        : levelsRange === 'deep'\n            ? [2, 6]\n            : levelsRange;\n    return buildTree(headers, high, low);\n}\nexport function useActiveAnchor(container, marker) {\n    const { isAsideEnabled } = useAside();\n    const onScroll = throttleAndDebounce(setActiveLink, 100);\n    let prevActiveLink = null;\n    onMounted(() => {\n        requestAnimationFrame(setActiveLink);\n        window.addEventListener('scroll', onScroll);\n    });\n    onUpdated(() => {\n        // sidebar update means a route change\n        activateLink(location.hash);\n    });\n    onUnmounted(() => {\n        window.removeEventListener('scroll', onScroll);\n    });\n    function setActiveLink() {\n        if (!isAsideEnabled.value) {\n            return;\n        }\n        const scrollY = window.scrollY;\n        const innerHeight = window.innerHeight;\n        const offsetHeight = document.body.offsetHeight;\n        const isBottom = Math.abs(scrollY + innerHeight - offsetHeight) < 1;\n        // resolvedHeaders may be repositioned, hidden or fix positioned\n        const headers = resolvedHeaders\n            .map(({ element, link }) => ({\n            link,\n            top: getAbsoluteTop(element)\n        }))\n            .filter(({ top }) => !Number.isNaN(top))\n            .sort((a, b) => a.top - b.top);\n        // no headers available for active link\n        if (!headers.length) {\n            activateLink(null);\n            return;\n        }\n        // page top\n        if (scrollY < 1) {\n            activateLink(null);\n            return;\n        }\n        // page bottom - highlight last link\n        if (isBottom) {\n            activateLink(headers[headers.length - 1].link);\n            return;\n        }\n        // find the last header above the top of viewport\n        let activeLink = null;\n        for (const { link, top } of headers) {\n            if (top > scrollY + getScrollOffset() + 4) {\n                break;\n            }\n            activeLink = link;\n        }\n        activateLink(activeLink);\n    }\n    function activateLink(hash) {\n        if (prevActiveLink) {\n            prevActiveLink.classList.remove('active');\n        }\n        if (hash == null) {\n            prevActiveLink = null;\n        }\n        else {\n            prevActiveLink = container.value.querySelector(`a[href=\"${decodeURIComponent(hash)}\"]`);\n        }\n        const activeLink = prevActiveLink;\n        if (activeLink) {\n            activeLink.classList.add('active');\n            marker.value.style.top = activeLink.offsetTop + 39 + 'px';\n            marker.value.style.opacity = '1';\n        }\n        else {\n            marker.value.style.top = '33px';\n            marker.value.style.opacity = '0';\n        }\n    }\n}\nfunction getAbsoluteTop(element) {\n    let offsetTop = 0;\n    while (element !== document.body) {\n        if (element === null) {\n            // child element is:\n            // - not attached to the DOM (display: none)\n            // - set to fixed position (not scrollable)\n            // - body or html element (null offsetParent)\n            return NaN;\n        }\n        offsetTop += element.offsetTop;\n        element = element.offsetParent;\n    }\n    return offsetTop;\n}\nfunction buildTree(data, min, max) {\n    resolvedHeaders.length = 0;\n    const result = [];\n    const stack = [];\n    data.forEach((item) => {\n        const node = { ...item, children: [] };\n        let parent = stack[stack.length - 1];\n        while (parent && parent.level >= node.level) {\n            stack.pop();\n            parent = stack[stack.length - 1];\n        }\n        if (node.element.classList.contains('ignore-header') ||\n            (parent && 'shouldIgnore' in parent)) {\n            stack.push({ level: node.level, shouldIgnore: true });\n            return;\n        }\n        if (node.level > max || node.level < min)\n            return;\n        resolvedHeaders.push({ element: node.element, link: node.link });\n        if (parent)\n            parent.children.push(node);\n        else\n            result.push(node);\n        stack.push(node);\n    });\n    return result;\n}\n", "import { computed, onMounted, onUnmounted, ref, watch, watchEffect, watchPostEffect } from 'vue';\nimport { isActive } from '../../shared';\nimport { hasActiveLink as containsActiveLink } from '../support/sidebar';\nimport { useData } from './data';\nconst isOpen = ref(false);\n/**\n * a11y: cache the element that opened the Sidebar (the menu button) then\n * focus that button again when <PERSON><PERSON> is closed with Escape key.\n */\nexport function useCloseSidebarOnEscape(close) {\n    let triggerElement;\n    watchEffect(() => {\n        triggerElement = isOpen.value\n            ? document.activeElement\n            : undefined;\n    });\n    onMounted(() => {\n        window.addEventListener('keyup', onEscape);\n    });\n    onUnmounted(() => {\n        window.removeEventListener('keyup', onEscape);\n    });\n    function onEscape(e) {\n        if (e.key === 'Escape' && isOpen.value) {\n            close();\n            triggerElement?.focus();\n        }\n    }\n}\nexport function useSidebarControl() {\n    function open() {\n        isOpen.value = true;\n    }\n    function close() {\n        isOpen.value = false;\n    }\n    function toggle() {\n        isOpen.value ? close() : open();\n    }\n    return {\n        isOpen,\n        open,\n        close,\n        toggle\n    };\n}\nexport function useSidebarItemControl(item) {\n    const { page, hash } = useData();\n    const collapsed = ref(false);\n    const collapsible = computed(() => {\n        return item.value.collapsed != null;\n    });\n    const isLink = computed(() => {\n        return !!item.value.link;\n    });\n    const isActiveLink = ref(false);\n    const updateIsActiveLink = () => {\n        isActiveLink.value = isActive(page.value.relativePath, item.value.link);\n    };\n    watch([page, item, hash], updateIsActiveLink);\n    onMounted(updateIsActiveLink);\n    const hasActiveLink = computed(() => {\n        if (isActiveLink.value) {\n            return true;\n        }\n        return item.value.items\n            ? containsActiveLink(page.value.relativePath, item.value.items)\n            : false;\n    });\n    const hasChildren = computed(() => {\n        return !!(item.value.items && item.value.items.length);\n    });\n    watchEffect(() => {\n        collapsed.value = !!(collapsible.value && item.value.collapsed);\n    });\n    watchPostEffect(() => {\n        ;\n        (isActiveLink.value || hasActiveLink.value) && (collapsed.value = false);\n    });\n    function toggle() {\n        if (collapsible.value) {\n            collapsed.value = !collapsed.value;\n        }\n    }\n    return {\n        collapsed,\n        collapsible,\n        isLink,\n        isActiveLink,\n        hasActiveLink,\n        hasChildren,\n        toggle\n    };\n}\n"], "mappings": ";;;;;;;;;AAAA,OAAO;;;ACAP,OAAO;AACP,OAAO;AACP,OAAO;AACP,OAAO;AACP,OAAO;AACP,OAAO;AACP,OAAO;AACP,OAAO;AACP,OAAO;AACP,OAAO,aAAa;AACpB,OAAO,YAAY;AACnB,SAAoB,WAAXA,gBAA0B;AACnC,SAAoB,WAAXA,gBAA2B;AACpC,SAAoB,WAAXA,gBAAqC;AAC9C,SAAoB,WAAXA,gBAA6B;AACtC,SAAoB,WAAXA,gBAAgC;AACzC,SAAoB,WAAXA,gBAAiC;AAC1C,SAAoB,WAAXA,gBAA6B;AACtC,SAAoB,WAAXA,gBAAiC;AAC1C,SAAoB,WAAXA,iBAA0B;AACnC,SAAoB,WAAXA,iBAAyB;AAClC,SAAoB,WAAXA,iBAAiC;AAC1C,SAAoB,WAAXA,iBAA+B;AACxC,SAAoB,WAAXA,iBAAgC;AACzC,SAAoB,WAAXA,iBAA6B;AACtC,SAAoB,WAAXA,iBAAgC;AACzC,SAAoB,WAAXA,iBAA6B;AACtC,SAAoB,WAAXA,iBAAoC;AAC7C,SAAoB,WAAXA,iBAAkC;;;AC5B3C,SAAS,WAAW,kBAAkB,gBAAgB;;;ACGtD,IAAM,kBAAkB,OAAO,mBAAmB;AAqM3C,SAAS,aAAa,SAAS;AAClC,QAAM,SAAS,QAAQ,OAAO,CAAC,UAAU,SAAS,KAAK,CAAC;AACxD,MAAI,OAAO,UAAU;AACjB,WAAO,QAAQ,CAAC;AACpB,QAAM,UAAU,IAAI,IAAI,OAAO,QAAQ,CAAC,UAAU,QAAQ,QAAQ,KAAK,CAAC,CAAC;AACzE,QAAM,eAAe,CAAC,GAAG,OAAO;AAChC,SAAO,IAAI,MAAM,CAAC,GAAG;AAAA;AAAA,IAEjB,IAAI,GAAG,MAAM;AACT,UAAI,SAAS;AACT,eAAO;AACX,aAAO,UAAU,GAAG,OACf,IAAI,CAAC,UAAU,MAAM,IAAI,CAAC,EAC1B,OAAO,CAAC,MAAM,MAAM,MAAS,CAAC;AAAA,IACvC;AAAA,IACA,MAAM;AACF,YAAM,IAAI,MAAM,+CAA+C;AAAA,IACnE;AAAA,IACA,IAAI,GAAG,MAAM;AACT,aAAO,QAAQ,IAAI,IAAI;AAAA,IAC3B;AAAA,IACA,UAAU;AACN,aAAO;AAAA,IACX;AAAA,IACA,yBAAyB,GAAG,MAAM;AAC9B,iBAAW,SAAS,QAAQ;AACxB,cAAM,aAAa,OAAO,yBAAyB,OAAO,IAAI;AAC9D,YAAI;AACA,iBAAO;AAAA,MACf;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AACA,UAAU,SAAS,SAAU,KAAK;AAC9B,SAAO,MAAM,eAAe;AAChC;AACO,SAAS,SAAS,OAAO;AAC5B,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AACrD;;;AC9OA,SAAS,gBAAgB;;;ACAzB,SAAS,WAAW,gBAAgB;AAC7B,IAAM,UAAU;;;AC6BhB,SAAS,iBAAiBC,UAAS;AACtC,QAAM,SAAS,CAAC;AAChB,MAAI,iBAAiB;AACrB,aAAW,SAASA,UAAS;AACzB,UAAM,OAAOA,SAAQ,KAAK;AAC1B,QAAI,KAAK,OAAO;AACZ,uBAAiB,OAAO,KAAK,IAAI;AACjC;AAAA,IACJ;AACA,QAAI,CAAC,OAAO,cAAc,GAAG;AACzB,aAAO,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC;AAAA,IAC7B;AACA,WAAO,cAAc,EAAE,MAAM,KAAK,IAAI;AAAA,EAC1C;AACA,SAAO;AACX;;;AC7CA,SAAS,uBAAuB;;;ACIhC,IAAM,SAAS,IAAI,KAAK;;;ANExB,IAAM,UAAU,WAAW,CAAC,CAAC;AAC7B,IAAM,UAAU,WAAW,CAAC,CAAC;AAC7B,IAAM,QAAQ,WAAW,KAAK;AACvB,SAAS,YAAY;AACxB,QAAM,EAAE,aAAa,OAAAC,OAAM,IAAI,QAAQ;AACvC,QAAM,SAAS,SAAS,MAAM;AAC1B,WAAO,CAAC,EAAE,YAAY,MAAM,UAAU,YAAY,MAAM,WAAW;AAAA,EACvE,CAAC;AACD,QAAM,aAAa,SAAS,MAAM;AAC9B,WAAQ,YAAY,MAAM,YAAY,SAClC,QAAQ,MAAM,SAAS,KACvB,CAAC,OAAO;AAAA,EAChB,CAAC;AACD,QAAM,mBAAmB,SAAS,MAAM,WAAW,SAAS,MAAM,KAAK;AACvE,QAAM,gBAAgB,SAAS,MAAM;AACjC,WAAO,WAAW,QAAQ,iBAAiB,QAAQ,KAAK,IAAI,CAAC;AAAA,EACjE,CAAC;AACD,QAAM,WAAW,SAAS,MAAM;AAC5B,QAAI,OAAO;AACP,aAAO;AACX,QAAI,YAAY,MAAM,SAAS;AAC3B,aAAO,CAAC,CAAC,YAAY,MAAM;AAC/B,WAAOA,OAAM,MAAM,UAAU;AAAA,EACjC,CAAC;AACD,QAAM,YAAY,SAAS,MAAM;AAC7B,QAAI,CAAC,SAAS;AACV,aAAO;AACX,WAAO,YAAY,MAAM,SAAS,OAC5BA,OAAM,MAAM,UAAU,SACtB,YAAY,MAAM,UAAU;AAAA,EACtC,CAAC;AACD,QAAM,cAAc,SAAS,MAAM;AAC/B,WAAO,QAAQ,MAAM,SAAS;AAAA,EAClC,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA,SAAS,gBAAgB,OAAO;AAAA,IAChC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,gBAAgB,OAAO;AAAA,IAChC;AAAA,EACJ;AACJ;;;ADrBA,IAAM,QAAQ;AAAA,EACV;AAAA,EACA,YAAY,CAAC,EAAE,IAAI,MAAM;AACrB,QAAI,UAAU,SAAS,OAAO;AAAA,EAClC;AACJ;AACA,IAAO,wBAAQ;", "names": ["default", "sidebar", "theme"]}