server:
  port: 8080

spring:
  application:
    name: arch-scope
  aop:
    auto: false # 暂时禁用AOP自动配置以解决启动问题
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
      - org.redisson.spring.starter.RedissonAutoConfiguration
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************
    username: root
    password: root
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 10
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      max-evictable-idle-time-millis: 900000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false

mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.archscope.domain.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto
      # 表名下划线命名
      table-underline: true

# RocketMQ配置已移除

logging:
  level:
    root: INFO
    com.archscope: DEBUG

# ArchScope应用配置
archscope:
  prompts:
    directory: prompts

  openai:
    api:
      key: ${OPENAI_API_KEY:your-api-key}
      model: gpt-4
  git:
    servers:
      # 默认配置
      default-config:
        supports-https: true
        supports-http: false
        suggest-https-on-ssh-failure: true
        https-url-template: "https://{host}/{owner}/{repo}.git"
        http-url-template: "http://{host}/{owner}/{repo}.git"

      # 具体服务器配置 - 使用列表格式避免点号问题
      server-list:
        - host: "github.com"
          supports-https: true
          supports-http: true
          suggest-https-on-ssh-failure: true
          # personal-access-token: "ghp_xxxxxxxxxxxxxxxxxxxx"  # GitHub个人访问令牌（可选配置）
          # token-username: "token"  # GitHub使用token作为用户名

        - host: "gitlab.com"
          supports-https: true
          supports-http: true
          suggest-https-on-ssh-failure: true
          # personal-access-token: "glpat-xxxxxxxxxxxxxxxxxxxx"  # GitLab个人访问令牌（可选配置）
          # token-username: "oauth2"  # GitLab推荐使用oauth2作为用户名

        - host: "gitlab.yeepay.com"
          supports-https: true
          supports-http: false
          suggest-https-on-ssh-failure: false # 不建议转换，因为内网可能有访问限制
          personal-access-token: "**************************" # 个人访问令牌
          token-username: "oauth2" # GitLab推荐使用oauth2作为用户名

        - host: "gitee.com"
          supports-https: true
          supports-http: true
          suggest-https-on-ssh-failure: true

        - host: "bitbucket.org"
          supports-https: true
          supports-http: false
          suggest-https-on-ssh-failure: true
