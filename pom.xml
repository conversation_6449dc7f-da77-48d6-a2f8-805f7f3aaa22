<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/>
    </parent>

    <groupId>com.archscope</groupId>
    <artifactId>arch-scope-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>arch-scope-parent</name>
    <description>Architecture Scope Project - Parent</description>

    <modules>
        <module>arch-scope-domain</module>
        <module>arch-scope-app</module>
        <module>arch-scope-facade</module>
        <module>arch-scope-infrastructure</module>
        <module>arch-scope-main</module>
    </modules>

    <properties>
        <java.version>8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        <mysql.version>8.2.0</mysql.version>
        <druid.version>1.2.21</druid.version>
        <!-- <rocketmq.version>2.3.3</rocketmq.version> -->

        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <lombok.version>1.18.30</lombok.version>
        <flyway.version>9.22.3</flyway.version>
        <jmh.version>1.33</jmh.version>
        <archunit.version>1.2.1</archunit.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Internal Module Dependencies -->
            <dependency>
                <groupId>com.archscope</groupId>
                <artifactId>arch-scope-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.archscope</groupId>
                <artifactId>arch-scope-app</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.archscope</groupId>
                <artifactId>arch-scope-facade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.archscope</groupId>
                <artifactId>arch-scope-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- MyBatis Plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- MySQL -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
                <scope>runtime</scope>
            </dependency>

            <!-- Druid -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- RocketMQ - 已移除 -->
            <!--
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>
            -->

            <!-- MapStruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <!-- Anthropic Java -->
            <dependency>
                <groupId>com.anthropic</groupId>
                <artifactId>anthropic-java</artifactId>
                <version>1.2.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <configuration>
                        <excludes>
                            <exclude>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </exclude>
                        </excludes>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>8</source>
                        <target>8</target>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.openjdk.jmh</groupId>
                                <artifactId>jmh-generator-annprocess</artifactId>
                                <version>${jmh.version}</version>
                            </path>
                            <path>
                                <groupId>com.tngtech.archunit</groupId>
                                <artifactId>archunit-junit5</artifactId>
                                <version>${archunit.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>

            <!-- Maven Surefire Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>-Dnet.bytebuddy.experimental=true</argLine>
                </configuration>
            </plugin>

            <!-- Flyway Maven Plugin -->
            <plugin>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-maven-plugin</artifactId>
                <version>${flyway.version}</version>
                <configuration>
                    <url>*************************************</url>
                    <user>root</user>
                    <password>root</password>
                    <locations>
                        <location>classpath:db/migration</location>
                    </locations>
                    <baselineOnMigrate>true</baselineOnMigrate>
                    <validateOnMigrate>true</validateOnMigrate>
                    <outOfOrder>false</outOfOrder>
                    <cleanDisabled>true</cleanDisabled>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.mysql</groupId>
                        <artifactId>mysql-connector-j</artifactId>
                        <version>8.2.0</version>
                    </dependency>
                    <dependency>
                        <groupId>org.flywaydb</groupId>
                        <artifactId>flyway-mysql</artifactId>
                        <version>${flyway.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
</project>